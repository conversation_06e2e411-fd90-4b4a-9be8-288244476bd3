# Copy this file to .env.local (or .env) and fill in your actual values.
# .env.local should be added to .gitignore

# --- Gemini API (Initial Prompt Generation) ---
VUE_APP_GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
VUE_APP_GEMINI_BASE_URL="https://openrouter.ai/api/v1"
VUE_APP_GEMINI_MODEL="gpt-4.1"
VUE_APP_INTERNAL_LIB_NAME="@xx-components"

# --- LongCat API (Prompt Refinement) ---
VUE_APP_LONGCAT_API_KEY="YOUR_LONGCAT_API_KEY_HERE"
VUE_APP_LONGCAT_BASE_URL="https://openrouter.ai/api/v1"
VUE_APP_LONGCAT_MODEL_REFINE="gpt-4.1"

# Note: For the provided code, Gemini and LongCat might share the same API key and base URL.
# If so, you can set VUE_APP_GEMINI_API_KEY and VUE_APP_LONGCAT_API_KEY to the same value.