<template>
  <div class="home">
    <el-row :gutter="20" class="hero-row">
      <el-col :span="24">
        <el-card class="hero-card">
          <h1 class="hero-title">Copy Coder</h1>
          <p class="hero-description">
            使用 AI 生成高质量的前端 Prompt，加速你的开发。
          </p>
        </el-card>
      </el-col>
    </el-row>

    <el-tabs v-model="activeTab" class="content-tabs">
      <el-tab-pane label="图片转代码 Prompt" name="imageToCode">
        <ImageToCodePanel
          ref="imageToCodePanelRef"
          :loading="isCodeGenerating"
          :initial-image-base64="loadedSessionData.initialImageBase64"
          :initial-form-settings="loadedSessionData.initialFormSettings"
          :prompt-versions="promptVersions"
          @file-updated="onFileUpdated"
          @settings-changed="onSettingsChanged"
          @generate-code-prompt-requested="handleGenerateCodePromptRequest"
          @save-session-requested="initiateSaveSession"
          @load-session-file-selected="handleLoadSessionFile"
          @open-chat-dialog="openChatDialog"
          @open-history-dialog="promptHistoryDialogVisible = true"
        />
      </el-tab-pane>
      <el-tab-pane label="PRD 转 Agent Prompt" name="prdToAgent">
        <PRDToAgentPanel ref="prdToAgentPanelRef" />
      </el-tab-pane>
    </el-tabs>

    <!-- Dialogs (remain in Home.vue as they are global to imageToCode flow) -->
    <ChatDialog
      :visible.sync="chatDialogVisible"
      :initial-prompt="generatedCodePrompt"
      :component-library="form.componentLibrary"
      :existing-chat-history="chatHistoryForDialog"
      :existing-refined-prompt-base="currentChatTurnBasePromptForDialog"
      @apply-prompt="handleApplyRefinedPrompt"
      @closed="onChatDialogClosed"
    />
    <PromptHistoryDialog
      :visible.sync="promptHistoryDialogVisible"
      :versions="promptVersions"
      @use-version="handleUsePromptVersion"
      @closed="promptHistoryDialogVisible = false"
    />
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex';
import ImageToCodePanel from '@/components/ImageToCodePanel.vue';
import PRDToAgentPanel from '@/components/PRDToAgentPanel.vue';
import ChatDialog from '@/components/ChatDialog.vue';
import PromptHistoryDialog from '@/components/PromptHistoryDialog.vue';

const SESSION_FORMAT_VERSION = '1.0.2';

export default {
  name: 'HomePage',
  components: {
    ImageToCodePanel,
    PRDToAgentPanel,
    ChatDialog,
    PromptHistoryDialog,
  },
  data() {
    return {
      activeTab: 'imageToCode',
      form: {
        appType: 'web',
        temperature: 0.5,
        componentLibrary: 'yxfe',
      },
      currentUploadedFileRaw: null,
      currentImageBase64: '',

      chatDialogVisible: false,
      chatHistoryForDialog: [],
      currentChatTurnBasePromptForDialog: '',

      promptHistoryDialogVisible: false,
      promptVersions: [],

      loadedSessionData: {
          initialImageBase64: '',
          initialFormSettings: null,
      }
    };
  },
  computed: {
    ...mapState({
        generatedCodePrompt: state => state.generatedCodePrompt,
        // loading: state => state.loading, // 移除全局 loading
        isCodeGenerating: state => state.isCodeGenerating, // 映射独立的 loading
        isPrdGenerating: state => state.isPrdGenerating,    // 映射独立的 loading
    }),
  },
  methods: {
    ...mapActions(['generateCodePrompt', 'generatePrdPrompt']),
    ...mapMutations(['SET_CODE_PROMPT', 'SET_PRD_PROMPT']),

    onFileUpdated({ base64Image, fileObject }) {
        this.currentImageBase64 = base64Image;
        this.currentUploadedFileRaw = fileObject;

        if (fileObject) {
            this.promptVersions = [];
            this.chatHistoryForDialog = [];
            this.currentChatTurnBasePromptForDialog = '';
            this.SET_CODE_PROMPT('');
        }
    },
    onSettingsChanged(settings) {
      this.form.appType = settings.appType;
      this.form.componentLibrary = settings.componentLibrary;
      this.form.temperature = settings.temperature / 100;
    },
    async handleGenerateCodePromptRequest(payload) {
      if (!payload.imageBase64) {
         this.$message.warning('没有图片数据可用于生成。'); // 友好的错误提示
         return;
      }
      if (payload.newRawFileObject) {
          this.promptVersions = [];
          this.chatHistoryForDialog = [];
          this.currentChatTurnBasePromptForDialog = '';
      }

      await this.generateCodePrompt({
        image: payload.imageBase64,
        appType: payload.appType,
        temperature: payload.temperature,
        componentLibrary: payload.componentLibrary,
      });

      this.$nextTick(() => {
        if (this.generatedCodePrompt) {
            if (this.promptVersions.length === 0 || this.promptVersions[this.promptVersions.length -1].content !== this.generatedCodePrompt) {
                 this.addPromptVersion(this.generatedCodePrompt, 'initial');
                 this.currentChatTurnBasePromptForDialog = this.generatedCodePrompt;
            }
        }
      });
    },

    openChatDialog() {
      if (this.chatHistoryForDialog.length === 0) {
          this.currentChatTurnBasePromptForDialog = this.generatedCodePrompt;
      }
      this.chatDialogVisible = true;
    },
    handleApplyRefinedPrompt(refinedPromptContent, updatedChatHistory, newBasePrompt) {
      if (refinedPromptContent) {
        this.SET_CODE_PROMPT(refinedPromptContent);
        this.addPromptVersion(refinedPromptContent, 'refined');
        this.$message.success('已应用优化后的提示词');
        this.chatHistoryForDialog = updatedChatHistory;
        this.currentChatTurnBasePromptForDialog = newBasePrompt || refinedPromptContent;
      }
      this.chatDialogVisible = false;
    },
    onChatDialogClosed() {
        this.chatDialogVisible = false;
    },

    addPromptVersion(content, source) {
      if (!content) return;
      const newVersion = {
        id: Date.now(),
        timestamp: Date.now(),
        content: content,
        source: source,
      };
      this.promptVersions.push(newVersion);
    },
    handleUsePromptVersion(content) {
      this.SET_CODE_PROMPT(content);
      this.currentChatTurnBasePromptForDialog = content;
      this.$message.success('已切换到选定版本的提示词。');
      this.promptHistoryDialogVisible = false;
    },

    initiateSaveSession(payload) {
      const sessionData = {
        formatVersion: SESSION_FORMAT_VERSION,
        createdAt: Date.now(),
        initialImageBase64: payload.currentImageBase64,
        initialFormSettings: payload.formSettings,
        promptVersions: this.promptVersions,
        chatHistory: this.chatHistoryForDialog,
        currentChatTurnBasePrompt: this.currentChatTurnBasePromptForDialog || this.generatedCodePrompt,
      };

      const jsonData = JSON.stringify(sessionData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `copycoder_session_${new Date().toISOString().slice(0,10)}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      this.$message.success('Session saved!');
    },
    handleLoadSessionFile(file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const sessionData = JSON.parse(e.target.result);
          if (sessionData.formatVersion !== SESSION_FORMAT_VERSION && sessionData.formatVersion !== '1.0.0' && sessionData.formatVersion !== '1.0.1') {
             this.$message.error(`会话文件版本不兼容。期望版本: ${SESSION_FORMAT_VERSION}, 1.0.0 或 1.0.1。`); // 友好的错误提示
            return;
          }
          this.loadSessionData(sessionData);
          this.$message.success('会话加载成功！'); // 友好的提示
        } catch (error) {
          this.$message.error('加载会话失败: 文件格式无效或已损坏。'); // 友好的错误提示
        }
      };
      reader.readAsText(file);
    },
    loadSessionData(data) {
      this.resetSessionState(true);

      this.loadedSessionData.initialImageBase64 = data.initialImageBase64 || '';
      this.loadedSessionData.initialFormSettings = data.initialFormSettings ? { ...data.initialFormSettings } : null;

      if (data.initialFormSettings) {
          this.form.appType = data.initialFormSettings.appType || 'web';
          this.form.componentLibrary = data.initialFormSettings.componentLibrary || 'yxfe';
          this.form.temperature = (data.initialFormSettings.temperature === undefined ? 50 : data.initialFormSettings.temperature) / 100;
      }
      this.currentImageBase64 = data.initialImageBase64 || '';
      this.currentUploadedFileRaw = null;


      this.promptVersions = data.promptVersions || [];
      this.chatHistoryForDialog = data.chatHistory || [];

      let activePrompt = data.currentChatTurnBasePrompt || '';
      if (!activePrompt && this.promptVersions.length > 0) {
          const latestVersion = [...this.promptVersions].sort((a,b) => b.timestamp - a.timestamp)[0];
          activePrompt = latestVersion.content;
      }
      this.SET_CODE_PROMPT(activePrompt);
      this.currentChatTurnBasePromptForDialog = activePrompt;

      this.chatDialogVisible = false;
      this.promptHistoryDialogVisible = false;

      this.activeTab = 'imageToCode';
    },
    resetSessionState(fullReset = true) {
        this.loadedSessionData.initialImageBase64 = '';
        this.loadedSessionData.initialFormSettings = null;

        this.currentImageBase64 = '';
        this.currentUploadedFileRaw = null;

        if (this.$refs.imageToCodePanelRef) {
            this.$refs.imageToCodePanelRef.clearUploadState();
        }
        if (this.$refs.prdToAgentPanelRef) {
            this.$refs.prdToAgentPanelRef.clearState();
        }

        this.SET_CODE_PROMPT('');
        this.SET_PRD_PROMPT('');
        this.promptVersions = [];
        this.chatHistoryForDialog = [];
        this.currentChatTurnBasePromptForDialog = '';

        if (fullReset) {
            this.form.appType = 'web';
            this.form.componentLibrary = 'yxfe';
            this.form.temperature = 0.5;
            this.loadedSessionData.initialFormSettings = {appType: 'web', componentLibrary: 'yxfe', temperature: 50};
        }
    }
  }
}
</script>

<style scoped>
.home {
  max-width: 1400px; /* 增加最大宽度以更好利用屏幕空间 */
  margin: 0 auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); /* 添加渐变背景 */
}

.hero-card {
  margin-bottom: 24px;
  padding: 32px 40px;
  flex-shrink: 0;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.hero-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #1a202c;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 18px;
  color: #4a5568;
  font-weight: 400;
  line-height: 1.6;
}

.content-tabs {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: visible; /* 改为visible，允许内容正常显示 */
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.content-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 24px;
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 16px 16px 0 0;
}

.content-tabs :deep(.el-tabs__nav-wrap) {
  padding: 16px 24px 0 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.content-tabs :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.content-tabs :deep(.el-tabs__nav) {
  border: none;
  float: none;
}

.content-tabs :deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
  color: #64748b;
  padding: 12px 20px;
  border: none;
  border-radius: 8px 8px 0 0;
  margin-right: 4px;
  margin-bottom: -1px;
  transition: all 0.3s ease;
  position: relative;
  background: transparent;
  height: auto;
  line-height: 1.4;
}

.content-tabs :deep(.el-tabs__item.is-active) {
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.9);
}

.content-tabs :deep(.el-tabs__active-bar) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 3px;
  border-radius: 2px;
  bottom: -1px;
  z-index: 1;
}

.content-tabs :deep(.el-tabs__content) {
  flex-grow: 1;
  overflow: visible; /* 改为visible，允许内容正常显示 */
  padding: 24px;
}

.content-tabs :deep(.el-tab-pane) {
  height: auto; /* 改为自动高度 */
  min-height: 750px; /* 设置最小高度 */
  display: flex;
  flex-direction: column;
  overflow: visible; /* 改为visible，允许内容正常显示 */
}

.el-row {
  flex-grow: 1;
  display: flex;
  align-items: flex-start;
  margin-left: 0 !important;
  margin-right: 0 !important;
  gap: 24px;
}

.card-column {
  display: flex;
  flex-direction: column;
}

.card-column:last-child {
  display: flex;
  flex-direction: column;
  height: auto; /* 改为自动高度，让内容决定高度 */
  min-height: 800px; /* 增加最小高度确保内容区域足够大 */
  overflow: visible; /* 改为visible，允许内容正常显示 */
}

.card-column > .el-card,
.card-column > .controls-card {
  width: 100%;
  height: auto; /* 改为自动高度 */
  min-height: 750px; /* 设置最小高度 */
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.card-column > .el-card:hover,
.card-column > .controls-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* 响应式设计优化 */
@media screen and (max-width: 1400px) {
  .home {
    max-width: 1200px;
    padding: 20px;
  }
}

@media screen and (max-width: 1200px) {
  .home {
    padding: 16px;
  }

  .hero-card {
    padding: 24px 32px;
  }

  .hero-title {
    font-size: 28px;
  }

  .content-tabs :deep(.el-tabs__content) {
    padding: 20px;
  }
}

@media screen and (max-width: 768px) {
  .home {
    padding: 12px;
    background: #f8fafc;
  }

  .hero-card {
    padding: 20px;
    margin-bottom: 16px;
  }

  .hero-title {
    font-size: 24px;
  }

  .hero-description {
    font-size: 16px;
  }

  .content-tabs {
    border-radius: 12px;
  }

  .content-tabs :deep(.el-tabs__header) {
    padding: 0 16px;
    border-radius: 12px 12px 0 0;
  }

  .content-tabs :deep(.el-tabs__nav-wrap) {
    padding: 12px 16px 0 16px;
  }

  .content-tabs :deep(.el-tabs__item) {
    font-size: 14px;
    padding: 10px 16px;
    margin-right: 2px;
    margin-bottom: -1px;
    border-radius: 6px 6px 0 0;
    height: auto;
    line-height: 1.4;
  }

  .content-tabs :deep(.el-tabs__content) {
    padding: 16px;
  }

  .el-row {
    flex-direction: column;
    gap: 16px;
  }

  .card-column {
    width: 100%;
  }

  .card-column:last-child {
    height: auto; /* 移动端使用自动高度 */
    min-height: 500px; /* 移动端最小高度 */
  }
}
</style>