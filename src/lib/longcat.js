import OpenAI from 'openai';

const LONGCAT_API_KEY = process.env.VUE_APP_LONGCAT_API_KEY;
const LONGCAT_BASE_URL = process.env.VUE_APP_LONGCAT_BASE_URL;
const LONGCAT_MODEL_REFINE = process.env.VUE_APP_LONGCAT_MODEL_REFINE || 'gpt-4.1';

// --- OpenAI Client Initialization ---
let longcatClient = null;
try {
  if (!LONGCAT_API_KEY || LONGCAT_API_KEY === 'YOUR_LONGCAT_API_KEY' || !LONGCAT_BASE_URL) {
    // LongCat API Key or Base URL not configured correctly
  } else {
    longcatClient = new OpenAI({
      apiKey: LONGCAT_API_KEY,
      baseURL: LONGCAT_BASE_URL,
      dangerouslyAllowBrowser: true
    });
  }
} catch (error) {
  longcatClient = null;
}

function handleApiError(error, context) {
  let message = `Failed during LongCat API call (${context}).`;
  if (error instanceof OpenAI.APIError) {
    message = `LongCat API Error (${context}): ${error.status} ${error.name} - ${error.message}`;

    if (error.error) {
        message += ` Details: ${JSON.stringify(error.error)}`;
    }
  } else if (error instanceof Error) {
    message = `LongCat Client Error (${context}): ${error.message}`;
  }
  throw new Error(message);
}

export async function streamLongCatResponse(
  currentFullPrompt,
  history,
  userTextMessage,
  imageBase64 = null,
  temperature = 0.2,
  componentLibrary = 'yxfe'
){
  if (!longcatClient) {
    throw new Error('LongCat client is not initialized. Cannot refine prompt.');
  }
  let displayInternalLibName = '@yxfe/components';
  if (componentLibrary === 'mtd') {
    displayInternalLibName = '@ss/mtd-vue2';
  } else if (componentLibrary === 'yxfe') {
    displayInternalLibName = process.env.VUE_APP_INTERNAL_LIB_NAME || '@yxfe/components';
  }
  // Construct the user's message content, including image if provided
  const userMessageContent = [{ type: 'text', text: userTextMessage }];
  if (imageBase64) {
    userMessageContent.push({
      type: 'image_url',
      image_url: { url: imageBase64 },
    });
  }

  // 检查是否是"继续"命令
  const isContinueCommand = userTextMessage.trim().toLowerCase() === '继续' ||
                           userTextMessage.trim().toLowerCase() === 'continue' ||
                           userTextMessage.trim() === '请继续你上一条未完成的回复。';

  let systemPrompt = '';
  if (isContinueCommand) {
    systemPrompt = `You are an expert frontend development assistant. Your primary goal is to help the user iteratively refine a detailed Vue 2 component generation prompt.
You will be given the "CURRENT PROMPT BASE" which is the master prompt we are working on.
The user has asked you to continue your previous response that was cut off.

**IMPORTANT: DO NOT REPEAT THE ENTIRE PROMPT FROM THE BEGINNING. ONLY CONTINUE FROM WHERE YOU LEFT OFF.**

1. Look at your last response and identify where it was cut off.
2. Continue generating content from that exact point, without repeating what you've already said.
3. Focus on completing the thought or section that was interrupted.
4. Maintain the same style, format, and language as your previous response.
5. If you were in the middle of a list, continue the list from the next item.
6. If you were in the middle of a paragraph, continue the paragraph.
7. If you were in the middle of a code example, continue the code example.

The primary component library for this project is **${displayInternalLibName}**. Ensure your suggestions and refinements prioritize components from this library.`;
  } else {
    systemPrompt = `You are an expert frontend development assistant. Your primary goal is to help the user iteratively refine a detailed Vue 2 component generation prompt.
You will be given the "CURRENT PROMPT BASE" which is the master prompt we are working on.
The user may provide feedback, new information, new images, or text from an uploaded document.
Your task is to refine the "CURRENT PROMPT BASE". The user will provide feedback, new images, or text from an uploaded document.
The primary component library for this project is **${displayInternalLibName}**. Ensure your suggestions and refinements prioritize components from this library.

**Processing Instructions:**

1.  **Maintain Core Structure:** The "CURRENT PROMPT BASE" (especially its sections like "Core Technical Requirements", "Project-Specific Adherence", "页面分析", "开发实现规划") provides the fundamental structure. **Your primary goal is to ENRICH this existing structure, NOT to replace it wholesale or drastically alter its main sections.**

2.  **Analyze User Inputs:**
    *   **Textual Feedback/Instructions:** Directly address user's specific requests for modification, addition, or removal within the existing prompt sections.
    *   **New Images:** If a new image is provided, analyze its visual elements. Integrate this analysis primarily into the "页面分析" section of the "CURRENT PROMPT BASE", updating or adding to existing visual descriptions.
    *   **Uploaded Document Content:**
        *   You will receive document text, often marked (e.g., "[来自用户上传的文档...]").
        *   **Focus on extracting KEY INTERACTION LOGIC, USER FLOWS, and CRITICAL BUSINESS RULES from this document that are NOT already obvious or detailed in the "CURRENT PROMPT BASE's" "页面分析" or "开发实现规划" sections.**
        *   **DO NOT simply copy or restructure the entire document content into the prompt.**
        *   **Instead, selectively integrate these extracted key interactions/rules into the "开发实现规划" section, specifically under "核心功能实现" or by suggesting new, concise points. You might also subtly enhance the "页面分析" section's "隐含细节与交互" or "交互与表单控件" parts if the document clarifies specific UI behaviors.**
        *   **Avoid introducing entirely new top-level sections from the document unless they represent a fundamentally new aspect not covered at all by the "CURRENT PROMPT BASE".** For example, do not replace the existing "开发实现规划" with a structure derived solely from the document.

3.  **Integration Strategy:**
    *   When integrating document information, translate abstract business logic into actionable development points (e.g., "User clicks X, then Y happens, requiring method Z and state A").
    *   If the document provides details like specific API endpoints or exact component names (especially from ${displayInternalLibName} or Element UI) that were missing or generic in the "CURRENT PROMPT BASE", you can update those specific details.
    *   **PRIORITIZE CLARITY AND CONCISENESS.** The final prompt should remain a developer-friendly guide, not a verbose reproduction of a requirements document.

4.  **Output:** You MUST output the COMPLETE, UPDATED prompt, reflecting the incremental changes based on user input, while preserving the core structure of the "CURRENT PROMPT BASE".
    *   Even if your response is long and you need to break it down, the final culmination of your response for THIS TURN must be the entire, new version of the prompt.
    *   DO NOT just describe the changes or provide only the modified parts.
    *   If you are interrupted or cannot finish, clearly indicate this and state that the user should ask you to "continue" or "complete the prompt".
5. If the user asks to remove something, remove it from the prompt. If they ask to add or modify, do so.
6. Maintain the overall structure and core requirements of the prompt (like using internal libraries e.g. **${displayInternalLibName}**, Element UI, etc.) unless specifically asked to change them.
The language of your generated response (the entire updated prompt) MUST match the primary language of the text visible in the user-provided image or their textual instructions.
`;
  }

  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    {
      role: 'assistant',
      content: isContinueCommand
        ? `I'll continue from where I left off without repeating the entire prompt.`
        : `Okay, I understand. Here is the CURRENT PROMPT BASE we will be working on for this turn:\n\n---\n\n${currentFullPrompt}\n\n---\n\nWhat changes or additions would you like to make? If you provide a new image, I will analyze it and integrate its details into this prompt.`,
    },
    ...history,
    {
      role: 'user',
      content: userMessageContent,
    },
  ];
  try {
    const stream = await longcatClient.chat.completions.create({
      model: LONGCAT_MODEL_REFINE,
      messages: messages,
      temperature: temperature,
      stream: true,
      max_tokens: 4000, // Ensure this is large enough for the full prompt
    });
    return stream;
  } catch (error) {
    handleApiError(error, 'refinement');
  }
}