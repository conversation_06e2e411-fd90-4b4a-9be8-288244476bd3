// src/components/ImageToCodePanel.vue
<template>
  <div class="image-to-code-panel">
    <el-row :gutter="24" class="main-row">
      <!-- 左侧：上传和设置区域 -->
      <el-col :lg="10" :md="11" :sm="24" class="left-column">
        <UploadAndSettings
          ref="uploadAndSettingsRef"
          :generate-button-loading="loading"
          :initial-image-base64="initialImageBase64"
          :initial-form-settings="initialFormSettings"
          @file-updated="onFileUpdated"
          @settings-changed="onSettingsChanged"
          @generate-prompt-requested="handleGenerateRequest"
          @save-session-requested="initiateSaveSession"
          @load-session-file-selected="handleLoadSessionFile"
        />
      </el-col>

      <!-- 右侧：结果展示区域 -->
      <el-col :lg="14" :md="13" :sm="24" class="right-column">
        <el-card class="result-card">
          <div slot="header" class="result-header">
            <div class="header-title">
              <i class="el-icon-document-copy header-icon"></i>
              <span class="header-text">生成的 Prompt</span>
            </div>
            <div class="header-actions" v-if="generatedPrompt && !loading">
              <el-button
                size="small"
                icon="el-icon-time"
                @click="$emit('open-history-dialog')"
                :disabled="promptVersions.length === 0"
                class="action-btn"
              >
                历史
              </el-button>
              <el-button
                size="small"
                type="primary"
                icon="el-icon-chat-dot-round"
                @click="$emit('open-chat-dialog')"
                class="action-btn"
              >
                优化
              </el-button>
              <el-button
                size="small"
                icon="el-icon-document-copy"
                @click="copyPrompt"
                class="action-btn copy-btn"
              >
                {{ copied ? '已复制' : '复制' }}
              </el-button>
            </div>
          </div>

          <div class="result-body">
            <!-- 空状态：只在没有内容且不在加载时显示 -->
            <div v-if="!generatedPrompt && !loading" class="empty-state">
              <div class="empty-icon">
                <i class="el-icon-magic-stick"></i>
              </div>
              <h3 class="empty-title">准备生成您的专属 Prompt</h3>
              <p class="empty-desc">上传 UI 设计图，配置生成参数，然后点击生成按钮开始创建高质量的前端开发 Prompt</p>
              <div class="empty-steps">
                <div class="step-item">
                  <div class="step-number">1</div>
                  <span>上传设计图</span>
                </div>
                <div class="step-item">
                  <div class="step-number">2</div>
                  <span>配置参数</span>
                </div>
                <div class="step-item">
                  <div class="step-number">3</div>
                  <span>生成 Prompt</span>
                </div>
              </div>
            </div>

            <!-- 初始加载状态：只在开始生成且还没有内容时显示 -->
            <div v-else-if="loading && !generatedPrompt" class="loading-container">
              <div class="loading-spinner">
                <i class="el-icon-loading"></i>
              </div>
              <p class="loading-text">正在生成 Prompt...</p>
              <div class="loading-progress">
                <div class="progress-bar"></div>
              </div>
            </div>

            <!-- 结果内容：有内容或正在生成时就显示，支持流式渲染 -->
            <div class="result-content" ref="promptContainer" v-if="generatedPrompt || loading">
              <div v-if="generatedPrompt" v-html="formattedPrompt"></div>
              <!-- 流式生成时的加载指示器 -->
              <div v-if="loading" class="streaming-indicator">
                <i class="el-icon-loading streaming-icon"></i>
                <span class="streaming-text">正在生成中...</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { marked } from 'marked';
import UploadAndSettings from '@/components/UploadAndSettings.vue';
import hljs from 'highlight.js'; // 导入 highlight.js
import 'highlight.js/styles/github.css'; // 选择一个代码高亮主题

export default {
  name: 'ImageToCodePanel',
  components: {
    UploadAndSettings,
  },
  props: {
    initialImageBase64: {
      type: String,
      default: ''
    },
    initialFormSettings: {
      type: Object,
      default: () => ({})
    },
    promptVersions: {
      type: Array,
      default: () => []
    },
    loading: { // 这个 loading 现在是 isCodeGenerating
        type: Boolean,
        default: false
    }
  },
  data() {
    return {
      copied: false,
      form: {
        appType: 'web',
        temperature: 0.5,
        componentLibrary: 'yxfe',
      },
      currentImageBase64: '',
      currentUploadedFileRaw: null,
    };
  },
  computed: {
    ...mapState({
        generatedPrompt: state => state.generatedCodePrompt,
    }),
    formattedPrompt() {
      if (!this.generatedPrompt) return '';
      return marked(this.generatedPrompt);
    }
  },
  watch: {
      initialImageBase64(newVal) {
          this.currentImageBase64 = newVal;
      },
      initialFormSettings: {
          handler(newVal) {
              if (newVal) {
                  this.form.appType = newVal.appType || 'web';
                  this.form.componentLibrary = newVal.componentLibrary || 'yxfe';
                  this.form.temperature = (newVal.temperature === undefined ? 50 : newVal.temperature) / 100;
              }
          },
          deep: true,
          immediate: true
      },
      // 监听 generatedPrompt 变化，实现流式渲染时的自动滚动
      generatedPrompt(newVal, oldVal) {
          this.$nextTick(() => {
              if (this.$refs.promptContainer) {
                  // 如果内容正在流式生成，滚动到底部以显示最新内容
                  if (this.loading && newVal && newVal.length > (oldVal ? oldVal.length : 0)) {
                      // 检查用户是否在底部附近（容忍度50px）
                      const container = this.$refs.promptContainer;
                      const isNearBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 50;

                      // 只有当用户在底部附近时才自动滚动，避免打断用户阅读
                      if (isNearBottom) {
                          container.scrollTop = container.scrollHeight;
                      }
                  }
              }
          });
      }
  },
  mounted() {
    // 初始化 marked，使用 highlight.js 进行代码高亮
    marked.setOptions({
      highlight: function(code, lang) {
        const language = hljs.getLanguage(lang) ? lang : 'plaintext';
        return hljs.highlight(code, { language }).value;
      },
      langPrefix: 'language-', // highlight.js css expects a top-level 'language-' class
      gfm: true, // 启用 GitHub Flavored Markdown
      breaks: true, // 启用换行符解析为 <br>
    });
  },
  methods: {
    ...mapActions(['generateCodePrompt']),
    marked,

    onFileUpdated({ base64Image, fileObject }) {
        this.currentImageBase64 = base64Image;
        this.currentUploadedFileRaw = fileObject;
        this.$emit('file-updated', { base64Image, fileObject });
    },
    onSettingsChanged(settings) {
      this.form.appType = settings.appType;
      this.form.componentLibrary = settings.componentLibrary;
      this.form.temperature = settings.temperature / 100;
      this.$emit('settings-changed', settings);
    },
    async handleGenerateRequest(payload) {
      if (!payload.imageBase64) {
         this.$message.warning('没有图片数据可用于生成。');
         return;
      }
      this.$emit('generate-code-prompt-requested', payload);
    },
    initiateSaveSession(uploadSettingsData) {
        this.$emit('save-session-requested', {
            ...uploadSettingsData,
            generatedCodePrompt: this.generatedPrompt,
            currentImageBase64: this.currentImageBase64,
            currentUploadedFileRaw: this.currentUploadedFileRaw,
        });
    },
    handleLoadSessionFile(file) {
        this.$emit('load-session-file-selected', file);
    },
    async copyPrompt() {
      try {
        await navigator.clipboard.writeText(this.generatedPrompt);
        this.copied = true;
        this.$message.success('已复制到剪贴板');
        setTimeout(() => {
          this.copied = false;
        }, 2000);
      } catch (error) {
        this.$message.error('复制失败');
      }
    },

    clearUploadState() {
        if (this.$refs.uploadAndSettingsRef) {
            this.$refs.uploadAndSettingsRef.clearUploadState();
        }
    }
  }
}
</script>

<style scoped>
.image-to-code-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.main-row {
  height: 100%;
  margin: 0 !important;
}

.left-column,
.right-column {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.left-column {
  padding-right: 12px !important;
}

.right-column {
  padding-left: 12px !important;
}

.result-card {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  transition: all 0.3s ease;
  height: auto; /* 改为自动高度 */
  min-height: 750px; /* 设置最小高度 */
  overflow: visible; /* 改为visible，允许内容正常显示 */
}

.result-card :deep(.el-card__body) {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 0 !important; /* 移除默认 padding，让 result-body 控制 */
  overflow: visible; /* 改为visible，允许内容正常显示 */
  height: auto; /* 允许自动高度 */
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 16px 16px 0 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.header-icon {
  font-size: 20px;
  color: #667eea;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.copy-btn.copied {
  background: #10b981 !important;
  border-color: #10b981 !important;
  color: white !important;
}

.result-body {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
  overflow: visible; /* 改为visible，允许内容正常显示 */
  min-height: 700px; /* 增加最小高度确保有足够空间 */
  height: auto; /* 允许自动高度 */
}

.result-content {
  flex-grow: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  min-height: 600px; /* 增加最小高度确保内容区域足够大 */
  height: auto; /* 允许自动高度 */
  max-height: none; /* 移除最大高度限制，让内容自由展示 */
  /* 确保滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

.result-content::-webkit-scrollbar {
  width: 6px;
}

.result-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.result-content::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
}

.result-content::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

.result-content :deep(h1),
.result-content :deep(h2),
.result-content :deep(h3),
.result-content :deep(h4),
.result-content :deep(h5),
.result-content :deep(h6) {
  color: #1a202c;
  font-weight: 700;
  margin: 24px 0 16px 0;
  line-height: 1.3;
}

.result-content :deep(h1) { font-size: 28px; }
.result-content :deep(h2) { font-size: 24px; }
.result-content :deep(h3) { font-size: 20px; }
.result-content :deep(h4) { font-size: 18px; }

.result-content :deep(p) {
  color: #374151;
  margin: 16px 0;
  font-size: 15px;
}

.result-content :deep(ul),
.result-content :deep(ol) {
  margin: 16px 0;
  padding-left: 24px;
}

.result-content :deep(li) {
  color: #374151;
  margin: 8px 0;
  font-size: 15px;
}

.result-content :deep(code) {
  /* background: rgba(102, 126, 234, 0.1); */
  /* color: #667eea; */
  padding: 2px 6px;
  border-radius: 6px;
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  font-size: 13px;
}

.result-content :deep(pre) {
  background: #1a202c;
  color: #e2e8f0;
  padding: 20px;
  border-radius: 12px;
  overflow-x: auto;
  margin: 20px 0;
  border: 1px solid #374151;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.result-content :deep(pre code) {
  background: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  font-size: 14px;
  line-height: 1.5;
}

.result-content :deep(blockquote) {
  border-left: 4px solid #667eea;
  background: rgba(102, 126, 234, 0.05);
  padding: 16px 20px;
  margin: 20px 0;
  border-radius: 0 8px 8px 0;
  color: #4a5568;
  font-style: italic;
}

.result-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-content :deep(th),
.result-content :deep(td) {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.result-content :deep(th) {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
}

.result-content :deep(tr:hover) {
  background: #f8fafc;
}

/* 加载状态优化 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  flex-grow: 1;
}

.loading-spinner {
  position: relative;
  margin-bottom: 24px;
}

.loading-spinner .el-icon-loading {
  font-size: 48px;
  color: #667eea;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 18px;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 24px;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

/* 空状态优化 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  flex-grow: 1;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.empty-icon i {
  font-size: 36px;
  color: #667eea;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 12px 0;
}

.empty-desc {
  font-size: 15px;
  color: #6b7280;
  line-height: 1.6;
  max-width: 400px;
  margin: 0 0 32px 0;
}

.empty-steps {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.step-item:hover {
  opacity: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.step-item span {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

/* 流式生成指示器 */
.streaming-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  margin-top: 16px;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
}

.streaming-icon {
  font-size: 16px;
  animation: spin 1s linear infinite;
}

.streaming-text {
  opacity: 0.8;
}

/* 响应式优化 */
@media screen and (max-width: 1200px) {
  .left-column {
    padding-right: 8px !important;
  }

  .right-column {
    padding-left: 8px !important;
  }

  .result-header {
    padding: 16px 20px;
  }

  .result-body {
    padding: 20px;
    min-height: 600px; /* 中等屏幕适配 */
  }

  .header-title {
    font-size: 16px;
  }

  .action-btn {
    font-size: 13px !important;
    padding: 6px 12px !important;
  }
}

@media screen and (max-width: 768px) {
  .main-row {
    flex-direction: column;
  }

  .left-column,
  .right-column {
    padding: 0 !important;
    margin-bottom: 16px;
  }

  .right-column {
    margin-bottom: 0;
  }

  .result-header {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .result-body {
    padding: 16px;
    min-height: 500px; /* 小屏幕适配 */
  }

  .result-content {
    padding: 16px;
    min-height: 450px; /* 小屏幕下的最小高度 */
    max-height: none; /* 移除最大高度限制 */
  }

  .result-content :deep(h1) { font-size: 24px; }
  .result-content :deep(h2) { font-size: 20px; }
  .result-content :deep(h3) { font-size: 18px; }
  .result-content :deep(h4) { font-size: 16px; }

  .result-content :deep(pre) {
    padding: 16px;
    font-size: 13px;
  }

  .empty-state {
    padding: 60px 16px;
  }

  .empty-steps {
    flex-direction: column;
    gap: 16px;
  }

  .step-item {
    flex-direction: row;
    gap: 12px;
  }

  .loading-container {
    padding: 60px 16px;
  }

  .loading-text {
    font-size: 16px;
  }

  .loading-progress {
    width: 150px;
  }
}

@media screen and (max-width: 480px) {
  .header-actions {
    flex-wrap: wrap;
    gap: 6px;
  }

  .action-btn {
    font-size: 12px !important;
    padding: 5px 10px !important;
  }

  .empty-title {
    font-size: 18px;
  }

  .empty-desc {
    font-size: 14px;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .step-item span {
    font-size: 12px;
  }
}
</style>