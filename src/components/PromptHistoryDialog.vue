<template>
  <div>
    <el-dialog
      title="Prompt Version History"
      :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      @closed="onDialogClosed"
    >
      <el-timeline v-if="sortedPromptVersions.length > 0">
        <el-timeline-item
          v-for="version in sortedPromptVersions"
          :key="version.id"
          :timestamp="new Date(version.timestamp).toLocaleString()"
          placement="top"
        >
          <el-card>
            <h4>{{ version.source === 'initial' ? 'Initial Generation' : (version.source === 'refined' ? 'Refinement' : 'Loaded/Edited') }}</h4>
            <p class="prompt-preview">{{ version.content.substring(0, 150) }}...</p>
            <el-button size="mini" @click="handleViewFullPromptVersion(version)">View Full</el-button>
            <el-button size="mini" type="primary" @click="handleUsePromptVersion(version)">Use this Version</el-button>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <el-empty description="No prompt versions saved yet." v-else></el-empty>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">Close</el-button>
      </span>
    </el-dialog>

    <!-- View Full Prompt Version Dialog (Internal to this component) -->
    <el-dialog
      title="Full Prompt Version"
      :visible.sync="viewFullPromptContentDialogVisible"
      width="70%"
      append-to-body
    >
      <div class="result-content-viewer" style="max-height: 60vh; overflow-y: auto;">
        <div v-if="currentViewingPromptContent" v-html="marked(currentViewingPromptContent)"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewFullPromptContentDialogVisible = false">Close</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { marked } from 'marked';

export default {
  name: 'PromptHistoryDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    versions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      viewFullPromptContentDialogVisible: false,
      currentViewingPromptContent: null,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      },
    },
    sortedPromptVersions() {
      return [...this.versions].sort((a, b) => b.timestamp - a.timestamp);
    },
  },
  methods: {
    marked, // Make marked available for the internal viewer
    handleViewFullPromptVersion(version) {
      this.currentViewingPromptContent = version.content;
      this.viewFullPromptContentDialogVisible = true;
    },
    handleUsePromptVersion(version) {
      this.$emit('use-version', version.content);
      this.dialogVisible = false; // Close history dialog after selection
    },
    onDialogClosed() {
        // Reset internal state if necessary when main dialog closes
        this.viewFullPromptContentDialogVisible = false;
        this.currentViewingPromptContent = null;
        this.$emit('closed');
    }
  },
};
</script>

<style scoped>
.prompt-preview {
  font-size: 0.9em;
  color: #666;
  white-space: pre-wrap; /* To respect newlines in preview */
  word-break: break-all; /* Was break-word, break-all is safer for previews */
  max-height: 60px; /* Or 3 lines */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Limit to 3 lines */
  -webkit-box-orient: vertical;
}
.el-timeline-item__timestamp { /* Ensure this style is scoped or global */
  font-size: 0.85em;
  color: #888;
}
.el-card h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

/* Styles for the internal full prompt viewer */
.result-content-viewer {
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace; /* Or your preferred mono font */
  border: 1px solid #e0e6ed;
  background-color: #fdfdfd;
}
.result-content-viewer :deep(h1),
.result-content-viewer :deep(h2),
.result-content-viewer :deep(h3) {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; /* Non-mono for headers */
}
.result-content-viewer :deep(p) {
  margin-bottom: 1em;
   font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; /* Non-mono for paragraphs */
  white-space: pre-wrap; /* Allow wrapping for paragraphs */
}
.result-content-viewer :deep(ul),
.result-content-viewer :deep(ol) {
  padding-left: 2em;
  margin-bottom: 1em;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; /* Non-mono for lists */
}
.result-content-viewer :deep(pre) {
  background-color: #f1f1f1;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre; /* Standard pre behavior */
}
.result-content-viewer :deep(code) {
  font-family: 'Courier New', monospace; /* Or your preferred mono font */
  background-color: #f1f1f1; /* Default for inline code */
  padding: 2px 4px;
  border-radius: 3px;
}
.result-content-viewer :deep(pre code) {
  background-color: transparent; /* Code inside pre should not have its own background */
  padding: 0;
}
</style>