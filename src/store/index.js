import Vue from 'vue'
import Vuex from 'vuex'
import { generatePrompt as apiGenerateCodePrompt, generatePrdPromptForAgent as apiGeneratePrdPrompt } from '@/lib/gemini'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    generatedCodePrompt: '',
    generatedPrdPrompt: '',
    // loading: false, // 移除全局 loading
    isCodeGenerating: false, // 新增：图片转代码的 loading 状态
    isPrdGenerating: false,  // 新增：PRD转Agent的 loading 状态
  },
  mutations: {
    SET_CODE_PROMPT(state, prompt) {
      if (typeof prompt === 'function') {
        state.generatedCodePrompt = prompt(state)
      } else {
        state.generatedCodePrompt = prompt
      }
    },
    APPEND_CODE_PROMPT(state, content) {
      state.generatedCodePrompt += content
    },
    SET_PRD_PROMPT(state, prompt) {
      if (typeof prompt === 'function') {
        state.generatedPrdPrompt = prompt(state)
      } else {
        state.generatedPrdPrompt = prompt
      }
    },
    APPEND_PRD_PROMPT(state, content) {
      state.generatedPrdPrompt += content
    },
    // SET_LOADING(state, loading) { // 移除全局 loading mutation
    //   state.loading = loading
    // }
    SET_CODE_GENERATING(state, status) { // 新增 mutation
      state.isCodeGenerating = status;
    },
    SET_PRD_GENERATING(state, status) {  // 新增 mutation
      state.isPrdGenerating = status;
    },
  },
  actions: {
    async generateCodePrompt({ commit }, { image, appType, temperature, componentLibrary }) {
      commit('SET_CODE_GENERATING', true); // 设置专属 loading
      commit('SET_CODE_PROMPT', '');

      try {
        const stream = await apiGenerateCodePrompt(image, appType, temperature, componentLibrary)
        if (!stream) {
          commit('SET_CODE_PROMPT', 'Error: No response from the model. Please try again.');
          return; // 不再设置 false，因为 finally 会处理
        }
        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content || '';
          if (content) {
            commit('APPEND_CODE_PROMPT', content);
          }
        }
      } catch (error) {
        commit('SET_CODE_PROMPT', `Error generating code prompt: ${error.message || '未知错误'}. 请检查您的API配置或稍后重试。`); // 友好的错误提示
      } finally {
        commit('SET_CODE_GENERATING', false); // 结束专属 loading
      }
    },
    async generatePrdPrompt({ commit }, { featureDescription, framework, uiLibrary, temperature }) {
      commit('SET_PRD_GENERATING', true); // 设置专属 loading
      commit('SET_PRD_PROMPT', '');

      try {
        const stream = await apiGeneratePrdPrompt(featureDescription, framework, uiLibrary, temperature);
        if (!stream) {
          commit('SET_PRD_PROMPT', 'Error: No response from the model. Please try again.');
          return; // 不再设置 false，因为 finally 会处理
        }
        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content || '';
          if (content) {
            commit('APPEND_PRD_PROMPT', content);
          }
        }
      } catch (error) {
        commit('SET_PRD_PROMPT', `Error generating PRD prompt: ${error.message || '未知错误'}. 请检查您的API配置或稍后重试。`); // 友好的错误提示
      } finally {
        commit('SET_PRD_GENERATING', false); // 结束专属 loading
      }
    }
  }
})