<template>
  <div id="app">
    <el-container>
      <el-header class="header">
        <div class="header-content">
          <div class="logo">Copy Coder</div>
        </div>
      </el-header>
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
/* 全局样式重置和优化 */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  height: 100%;
  margin: 0;
  padding: 0;
}

#app {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  display: flex;
  flex-direction: column;
  line-height: 1.6;
  color: #1a202c;
}

.el-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  position: fixed;
  width: 100%;
  z-index: 1000;
  top: 0;
  flex-shrink: 0;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.el-main {
  margin-top: 60px;
  padding: 0;
  flex-grow: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Element UI 全局样式优化 */
.el-card {
  border: none !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08) !important;
  border-radius: 16px !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
}

.el-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12) !important;
}

.el-card__header {
  background: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06) !important;
  border-radius: 16px 16px 0 0 !important;
  padding: 20px 24px !important;
  font-weight: 600 !important;
  color: #1a202c !important;
}

.el-card__body {
  padding: 24px !important;
}

/* 按钮样式优化 */
.el-button {
  border-radius: 12px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  border: 2px solid transparent !important;
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
}

.el-button--primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

.el-button--default {
  background: white !important;
  border: 2px solid #e5e7eb !important;
  color: #374151 !important;
}

.el-button--default:hover {
  border-color: #667eea !important;
  color: #667eea !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15) !important;
}

/* 输入框样式优化 */
.el-input__inner {
  border: 2px solid #e5e7eb !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
  font-size: 14px !important;
  padding: 12px 16px !important;
}

.el-input__inner:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* 选择器样式优化 */
.el-select .el-input__inner {
  cursor: pointer !important;
}

.el-select-dropdown {
  border-radius: 12px !important;
  border: 2px solid #e5e7eb !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
  margin-top: 4px !important;
}

.el-select-dropdown__item {
  padding: 12px 16px !important;
  transition: all 0.2s ease !important;
}

.el-select-dropdown__item:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  color: #667eea !important;
}

.el-select-dropdown__item.selected {
  background: rgba(102, 126, 234, 0.15) !important;
  color: #667eea !important;
  font-weight: 600 !important;
}

/* 消息提示样式优化 */
.el-message {
  border-radius: 12px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
}

.el-message--success {
  background: rgba(16, 185, 129, 0.9) !important;
}

.el-message--error {
  background: rgba(239, 68, 68, 0.9) !important;
}

.el-message--warning {
  background: rgba(245, 158, 11, 0.9) !important;
}

/* 滚动条样式优化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* 响应式优化 */
@media screen and (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .logo {
    font-size: 20px;
  }

  .el-card__header {
    padding: 16px 20px !important;
    font-size: 16px !important;
  }

  .el-card__body {
    padding: 20px !important;
  }

  .el-button {
    font-size: 14px !important;
    padding: 10px 16px !important;
  }
}
</style>
