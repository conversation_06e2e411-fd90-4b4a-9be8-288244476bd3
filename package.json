{"name": "super-copy-coder-vue", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@techstark/opencv-js": "^4.10.0-release.1", "axios": "^1.9.0", "browser-image-compression": "^2.0.2", "core-js": "^3.8.3", "easymde": "^2.20.0", "element-ui": "^2.15.14", "highlight.js": "^11.11.1", "js-tiktoken": "^1.0.20", "mammoth": "^1.9.0", "marked": "^4.3.0", "openai": "^4.96.0", "tesseract.js": "^6.0.1", "vue": "^2.6.14", "vue-easymde": "^2.0.0", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "util": "^0.12.5", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}