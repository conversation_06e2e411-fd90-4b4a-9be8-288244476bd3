# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@achrinza/node-ipc@^9.2.5":
  version "9.2.9"
  resolved "http://r.npm.sankuai.com/@achrinza/node-ipc/download/@achrinza/node-ipc-9.2.9.tgz"
  integrity sha1-q0gV2bFvHIOkef6HkVIqOr67HGo=
  dependencies:
    "@node-ipc/js-queue" "2.0.3"
    event-pubsub "4.3.0"
    js-message "1.0.7"

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@7.12.11":
  version "7.12.11"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.26.2", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.27.2.tgz"
  integrity sha1-QYP55kL9hOdOPup/+pOkEuOxAsk=

"@babel/core@^7.12.16":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.27.1.tgz"
  integrity sha1-id5R6GvRIkYAPjUkcExJVBsWw+Y=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.1"
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helpers" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/template" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/eslint-parser@^7.12.16":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/eslint-parser/download/@babel/eslint-parser-7.27.1.tgz"
  integrity sha1-4Ub7L6zvYsbF0ab9B8/Xnun3sPE=
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    eslint-visitor-keys "^2.1.0"
    semver "^6.3.1"

"@babel/generator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.27.1.tgz"
  integrity sha1-hi1PrYWPcgjt1IfCi1gUQDa3YjA=
  dependencies:
    "@babel/parser" "^7.27.1"
    "@babel/types" "^7.27.1"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.27.1.tgz"
  integrity sha1-Q0XYGppGpkhuJNBpRp8T5gRFwF0=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-compilation-targets@^7.12.16", "@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.27.1", "@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.27.1.tgz"
  integrity sha1-W+5CYqbqXdyFLQgGGZ6xfKPekoE=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.27.1.tgz"
  integrity sha1-BbCILZe6HU0DUZ5LzmFdcK+hjFM=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    regexpu-core "^6.2.0"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.3", "@babel/helper-define-polyfill-provider@^0.6.4":
  version "0.6.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.6.4.tgz"
  integrity sha1-Feh0Y2i/pnF4X1km/3SzBkwpH6s=
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-member-expression-to-functions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.27.1.tgz"
  integrity sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.25.9", "@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.27.1.tgz"
  integrity sha1-4WY7i3HS3pSNpcT7KiDKTz7Cem8=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-optimise-call-expression@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.27.1.tgz"
  integrity sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.26.5", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-remap-async-to-generator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.27.1.tgz"
  integrity sha1-RgHVx84usq6lgyjUNyVSP802LOY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-wrap-function" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-replace-supers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.27.1.tgz"
  integrity sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.27.1.tgz"
  integrity sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.25.9", "@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helper-wrap-function@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.27.1.tgz"
  integrity sha1-uIKFAJwxQnrzGNT+N2Uc1ioUJAk=
  dependencies:
    "@babel/template" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helpers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.27.1.tgz"
  integrity sha1-/8JwEwOGB826MojmksNhHAahiqQ=
  dependencies:
    "@babel/template" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/highlight@^7.10.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/highlight/download/@babel/highlight-7.25.9.tgz"
  integrity sha1-gUHOaPxzdXlG+YOzQ/EjH0aRrMY=
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/parser@^7.23.5", "@babel/parser@^7.26.9", "@babel/parser@^7.27.1", "@babel/parser@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.27.2.tgz"
  integrity sha1-V3UYvtsXos5CEq/QUuAfffCUESc=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-firefox-class-in-computed-class-key/download/@babel/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz"
  integrity sha1-Yd2KjmH361aCaNG18SnaPu42S/k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-class-field-initializer-scope/download/@babel/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz"
  integrity sha1-Q/cKbX79UjcO7731WuA9kbKThW0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz"
  integrity sha1-vrYjvVc7i28wR70EwyUGrcPlinI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz"
  integrity sha1-4TSlR56yupwCcU6MHr8eyQdhJP0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/download/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.27.1.tgz"
  integrity sha1-uxwlrzTXURXOIpod5/pEv4+VVnA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-proposal-class-properties@^7.12.13":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz"
  integrity sha1-sRD1l0GJX37CGm//aW7EYmXERqM=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-decorators@^7.12.13":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.27.1.tgz"
  integrity sha1-Nob0JLL4sv7nV5qk3xM6T1JEpZY=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-decorators" "^7.27.1"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz"
  integrity sha1-eET5KJVG76n+usLeTP41igUL1wM=

"@babel/plugin-syntax-decorators@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.27.1.tgz"
  integrity sha1-7n3ZWQruvAX51MjAVgAHsFl5pj0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-import-assertions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.27.1.tgz"
  integrity sha1-iIlK79KwO17mrRVip8jhWHSWrs0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-attributes@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.27.1.tgz"
  integrity sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-jsx@^7.12.13", "@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.25.9":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.27.1.tgz"
  integrity sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz"
  integrity sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.27.1.tgz"
  integrity sha1-biBhBnujqwJm2DSp+UgRGW8qupo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-async-generator-functions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-async-generator-functions/download/@babel/plugin-transform-async-generator-functions-7.27.1.tgz"
  integrity sha1-ykM9+YPWjhN1OY58pxvypPb9idc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-async-to-generator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.27.1.tgz"
  integrity sha1-mpOJO5N5s5Rmx0R09VrwPeeMZuc=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"

"@babel/plugin-transform-block-scoped-functions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.27.1.tgz"
  integrity sha1-VYqdbiTPcoAt07YqS1Hg1iwPV/k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-block-scoping@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.27.1.tgz"
  integrity sha1-vA2+isbeVgKYG6WO9oxt+O+b+7M=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-properties@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-class-properties/download/@babel/plugin-transform-class-properties-7.27.1.tgz"
  integrity sha1-3UCmo3Df1J0yNiriBt2vK7CCqSU=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-static-block@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-class-static-block/download/@babel/plugin-transform-class-static-block-7.27.1.tgz"
  integrity sha1-fpINViWyW7zNMGGu+8wFgF7VbOQ=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-classes@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.27.1.tgz"
  integrity sha1-A7sEvqLHsvcR8NtzBKjaRqhcztQ=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.27.1.tgz"
  integrity sha1-gWYueL9ec0qXmCwrfwp5MojvPKo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/template" "^7.27.1"

"@babel/plugin-transform-destructuring@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.27.1.tgz"
  integrity sha1-1ZFu9wicslTfBBiuUkUzwbcrplY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-dotall-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.27.1.tgz"
  integrity sha1-qmgh3oZMUosf7PKG8KF0446Cb00=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-keys@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.27.1.tgz"
  integrity sha1-8fv2KOzhjhLnsysXWUDmg1j1RtE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-named-capturing-groups-regex/download/@babel/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz"
  integrity sha1-UEOFTKYgqUFJNy5pAw/4y2qesOw=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-dynamic-import@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-dynamic-import/download/@babel/plugin-transform-dynamic-import-7.27.1.tgz"
  integrity sha1-THjzVVKsDgaqH248Vz1naV6K9aQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-exponentiation-operator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.27.1.tgz"
  integrity sha1-/El7EtgnflWXR/Wj7YaN2AZPg+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-export-namespace-from@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-export-namespace-from/download/@babel/plugin-transform-export-namespace-from-7.27.1.tgz"
  integrity sha1-ccpp00ce3W2qcRz038NABBXfnCM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-for-of@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.27.1.tgz"
  integrity sha1-vCT3CA6f9yG2OnCseyVkyhW2xAo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-function-name@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.27.1.tgz"
  integrity sha1-TQvzB3IOTc5tfDD8sf1sp3ves6c=
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-json-strings@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-json-strings/download/@babel/plugin-transform-json-strings-7.27.1.tgz"
  integrity sha1-ouDObvJWN2vVJ/KQ2gI5g1J6T0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-literals@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.27.1.tgz"
  integrity sha1-uq76TRCh1CBvnc3aUNfVgnu3CyQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-logical-assignment-operators@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-logical-assignment-operators/download/@babel/plugin-transform-logical-assignment-operators-7.27.1.tgz"
  integrity sha1-iQyyDgJw4OW+vj8CW0NIQcMtW6o=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-member-expression-literals@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.27.1.tgz"
  integrity sha1-N7iLpZTYUkGOmVNvVhL3lfI66vk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-amd@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.27.1.tgz"
  integrity sha1-pBRfnYfCKR/i0F+ZS2XbpOPnGW8=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-commonjs@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.27.1.tgz"
  integrity sha1-jkTtN8J4fswjvcNn9Jl3R2YU6DI=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-systemjs@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.27.1.tgz"
  integrity sha1-AOBbYYYwcNDzKSoAEmwWwOAkxO0=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-modules-umd@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.27.1.tgz"
  integrity sha1-Y/LPT23BXevBL2lORHFIY9NM0zQ=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.27.1.tgz"
  integrity sha1-8yuPeBjY/AzEbuIKjvdfBxr5duE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-new-target@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.27.1.tgz"
  integrity sha1-JZxDk5coytFwasFzUbfmp76hq+s=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-nullish-coalescing-operator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-nullish-coalescing-operator/download/@babel/plugin-transform-nullish-coalescing-operator-7.27.1.tgz"
  integrity sha1-T50xU79ngtc91CeFqdItAxl7yR0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-numeric-separator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-numeric-separator/download/@babel/plugin-transform-numeric-separator-7.27.1.tgz"
  integrity sha1-YU4LFcyADlmX2t2b1upSTtbIGcY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-object-rest-spread@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-rest-spread/download/@babel/plugin-transform-object-rest-spread-7.27.2.tgz"
  integrity sha1-Z/mrgiNHqivO6R6JlnY9p5veqXM=
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.27.1"
    "@babel/plugin-transform-parameters" "^7.27.1"

"@babel/plugin-transform-object-super@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.27.1.tgz"
  integrity sha1-HJMs0nvzh0xDpcrE9D6/lwyYcbU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"

"@babel/plugin-transform-optional-catch-binding@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-optional-catch-binding/download/@babel/plugin-transform-optional-catch-binding-7.27.1.tgz"
  integrity sha1-hMc0Hr3jXM02sTfp5FhmglByoww=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-optional-chaining@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-optional-chaining/download/@babel/plugin-transform-optional-chaining-7.27.1.tgz"
  integrity sha1-h0zjxPBrd4BZLpRgJut2oygwRU8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-parameters@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.27.1.tgz"
  integrity sha1-gDNLVLmxrFJEFVoMgwShh6YY1ac=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-methods@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-private-methods/download/@babel/plugin-transform-private-methods-7.27.1.tgz"
  integrity sha1-/ay6scXtgexw39u4shPWXaFItq8=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-property-in-object@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-private-property-in-object/download/@babel/plugin-transform-private-property-in-object-7.27.1.tgz"
  integrity sha1-TbvvKDtbLwGiHoHimfduNfkA+xE=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-property-literals@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.27.1.tgz"
  integrity sha1-B+r9YYgAWR6IBzoK8blA2aQsZCQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regenerator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.27.1.tgz"
  integrity sha1-Ckcd+SE0FuRM1mv2cXa2b2V2hAE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regexp-modifiers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regexp-modifiers/download/@babel/plugin-transform-regexp-modifiers-7.27.1.tgz"
  integrity sha1-35ulV3yXTj8USYiLcLdhaZmKbQk=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-reserved-words@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.27.1.tgz"
  integrity sha1-QPukh4zL0cVmBaRHmjqJGsAnS7Q=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-runtime@^7.12.15":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.27.1.tgz"
  integrity sha1-+fv3GUmiCesms+YDdbHZVpN7i+k=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.11.0"
    babel-plugin-polyfill-regenerator "^0.6.1"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.27.1.tgz"
  integrity sha1-Uyq9rN7Ie/7h4O+OL83uVD/jK5A=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-spread@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.27.1.tgz"
  integrity sha1-GiZNX8EnUJGPUOP+PiTkNxeKuwg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-sticky-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.27.1.tgz"
  integrity sha1-GJhJNdnSKWhDpJHXigFJOffc0oA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-template-literals@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.27.1.tgz"
  integrity sha1-Gg6zXYuz5u/AbJ/UDrC871SDKLg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typeof-symbol@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.27.1.tgz"
  integrity sha1-cOlmu0kuA1Cc836vptzDBR+EQ2k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-escapes@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.27.1.tgz"
  integrity sha1-PjFD+EOK74Qt4ogW7OWHgBkM+AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-property-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-property-regex/download/@babel/plugin-transform-unicode-property-regex-7.27.1.tgz"
  integrity sha1-vf4tMXDHjFaRo8O+k0yMAIdSWVY=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.27.1.tgz"
  integrity sha1-JZSPXDldsV9gkCjjcGZ+2Lrpr5c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-sets-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-sets-regex/download/@babel/plugin-transform-unicode-sets-regex-7.27.1.tgz"
  integrity sha1-arcG0Q+AG1xy2ouyVIVh+gQZPNE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/preset-env@^7.12.16":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/preset-env/download/@babel/preset-env-7.27.2.tgz"
  integrity sha1-EG5r+tkrWRsfb3b9TPE7dyWnv5o=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.27.1"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.27.1"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.27.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.27.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.27.1"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions" "^7.27.1"
    "@babel/plugin-syntax-import-attributes" "^7.27.1"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.27.1"
    "@babel/plugin-transform-async-generator-functions" "^7.27.1"
    "@babel/plugin-transform-async-to-generator" "^7.27.1"
    "@babel/plugin-transform-block-scoped-functions" "^7.27.1"
    "@babel/plugin-transform-block-scoping" "^7.27.1"
    "@babel/plugin-transform-class-properties" "^7.27.1"
    "@babel/plugin-transform-class-static-block" "^7.27.1"
    "@babel/plugin-transform-classes" "^7.27.1"
    "@babel/plugin-transform-computed-properties" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.27.1"
    "@babel/plugin-transform-dotall-regex" "^7.27.1"
    "@babel/plugin-transform-duplicate-keys" "^7.27.1"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-dynamic-import" "^7.27.1"
    "@babel/plugin-transform-exponentiation-operator" "^7.27.1"
    "@babel/plugin-transform-export-namespace-from" "^7.27.1"
    "@babel/plugin-transform-for-of" "^7.27.1"
    "@babel/plugin-transform-function-name" "^7.27.1"
    "@babel/plugin-transform-json-strings" "^7.27.1"
    "@babel/plugin-transform-literals" "^7.27.1"
    "@babel/plugin-transform-logical-assignment-operators" "^7.27.1"
    "@babel/plugin-transform-member-expression-literals" "^7.27.1"
    "@babel/plugin-transform-modules-amd" "^7.27.1"
    "@babel/plugin-transform-modules-commonjs" "^7.27.1"
    "@babel/plugin-transform-modules-systemjs" "^7.27.1"
    "@babel/plugin-transform-modules-umd" "^7.27.1"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-new-target" "^7.27.1"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.27.1"
    "@babel/plugin-transform-numeric-separator" "^7.27.1"
    "@babel/plugin-transform-object-rest-spread" "^7.27.2"
    "@babel/plugin-transform-object-super" "^7.27.1"
    "@babel/plugin-transform-optional-catch-binding" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"
    "@babel/plugin-transform-parameters" "^7.27.1"
    "@babel/plugin-transform-private-methods" "^7.27.1"
    "@babel/plugin-transform-private-property-in-object" "^7.27.1"
    "@babel/plugin-transform-property-literals" "^7.27.1"
    "@babel/plugin-transform-regenerator" "^7.27.1"
    "@babel/plugin-transform-regexp-modifiers" "^7.27.1"
    "@babel/plugin-transform-reserved-words" "^7.27.1"
    "@babel/plugin-transform-shorthand-properties" "^7.27.1"
    "@babel/plugin-transform-spread" "^7.27.1"
    "@babel/plugin-transform-sticky-regex" "^7.27.1"
    "@babel/plugin-transform-template-literals" "^7.27.1"
    "@babel/plugin-transform-typeof-symbol" "^7.27.1"
    "@babel/plugin-transform-unicode-escapes" "^7.27.1"
    "@babel/plugin-transform-unicode-property-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-sets-regex" "^7.27.1"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.11.0"
    babel-plugin-polyfill-regenerator "^0.6.1"
    core-js-compat "^3.40.0"
    semver "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "http://r.npm.sankuai.com/@babel/preset-modules/download/@babel/preset-modules-0.1.6-no-external-plugins.tgz"
  integrity sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/runtime@^7.12.13":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.27.1.tgz"
  integrity sha1-n84xPRLJp3UH8mTedGJuh/0NxUE=

"@babel/template@^7.26.9", "@babel/template@^7.27.1":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.27.2.tgz"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.26.9", "@babel/traverse@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.27.1.tgz"
  integrity sha1-TbdykCsTO73dHE96fuR3YcG58pE=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/template" "^7.27.1"
    "@babel/types" "^7.27.1"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.26.9", "@babel/types@^7.27.1", "@babel/types@^7.4.4":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.27.1.tgz"
  integrity sha1-ne/FPBb8iZ5GlB/GkBqe6hydhWA=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@discoveryjs/json-ext@0.5.7":
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/@discoveryjs/json-ext/download/@discoveryjs/json-ext-0.5.7.tgz"
  integrity sha1-HVcr+74Ut3BOC6Dzm3SBW4SHDXA=

"@eslint/eslintrc@^0.4.3":
  version "0.4.3"
  resolved "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz"
  integrity sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@hapi/hoek@^9.0.0", "@hapi/hoek@^9.3.0":
  version "9.3.0"
  resolved "http://r.npm.sankuai.com/@hapi/hoek/download/@hapi/hoek-9.3.0.tgz"
  integrity sha1-g2iGnctzW+Ln9ct2R9544WeiUfs=

"@hapi/topo@^5.1.0":
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/@hapi/topo/download/@hapi/topo-5.1.0.tgz"
  integrity sha1-3ESOMyxsbjek3AL9hLqNRLmvsBI=
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@humanwhocodes/config-array@^0.5.0":
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz"
  integrity sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk=
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz"
  integrity sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz"
  integrity sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz"
  integrity sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  resolved "http://r.npm.sankuai.com/@jridgewell/source-map/download/@jridgewell/source-map-0.3.6.tgz"
  integrity sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz"
  integrity sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz"
  integrity sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@leichtgewicht/ip-codec@^2.0.1":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@leichtgewicht/ip-codec/download/@leichtgewicht/ip-codec-2.0.5.tgz"
  integrity sha1-T8VsFcWAua233DwzOhNOVAtEv7E=

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  version "5.1.1-v1"
  resolved "http://r.npm.sankuai.com/@nicolo-ribaudo/eslint-scope-5-internals/download/@nicolo-ribaudo/eslint-scope-5-internals-5.1.1-v1.tgz"
  integrity sha1-2/czqWXKR7GXMXfcC7bIie3PsSk=
  dependencies:
    eslint-scope "5.1.1"

"@node-ipc/js-queue@2.0.3":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@node-ipc/js-queue/download/@node-ipc/js-queue-2.0.3.tgz"
  integrity sha1-rH/jPXZvpT4jPvj+2vNEOgHFpM0=
  dependencies:
    easy-stack "1.0.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@polka/url@^1.0.0-next.24":
  version "1.0.0-next.29"
  resolved "http://r.npm.sankuai.com/@polka/url/download/@polka/url-1.0.0-next.29.tgz"
  integrity sha1-WkAQmhq1+E1v2PySixnzZ8vn57E=

"@sideway/address@^4.1.5":
  version "4.1.5"
  resolved "http://r.npm.sankuai.com/@sideway/address/download/@sideway/address-4.1.5.tgz"
  integrity sha1-S8FJoAdmI87ZnKggi6eA1lqZudU=
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@sideway/formula@^3.0.1":
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/@sideway/formula/download/@sideway/formula-3.0.1.tgz"
  integrity sha1-gPy8uvfOAx4O8t0psb/Hw/WDYR8=

"@sideway/pinpoint@^2.0.0":
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/@sideway/pinpoint/download/@sideway/pinpoint-2.0.0.tgz"
  integrity sha1-z/j/rcNyrSn9P3gneusp5jLMcN8=

"@soda/friendly-errors-webpack-plugin@^1.8.0":
  version "1.8.1"
  resolved "http://r.npm.sankuai.com/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.8.1.tgz"
  integrity sha1-TU+7EQiZOqo2IRYkfD0YGIosbIU=
  dependencies:
    chalk "^3.0.0"
    error-stack-parser "^2.0.6"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

"@soda/get-current-script@^1.0.2":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/@soda/get-current-script/download/@soda/get-current-script-1.0.2.tgz"
  integrity sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc=

"@techstark/opencv-js@^4.10.0-release.1":
  version "4.10.0-release.1"
  resolved "http://r.npm.sankuai.com/@techstark/opencv-js/download/@techstark/opencv-js-4.10.0-release.1.tgz"
  integrity sha1-U0THFcQEwiQZrIk8ai2Gcn4kFvw=

"@trysound/sax@0.2.0":
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/@trysound/sax/download/@trysound/sax-0.2.0.tgz"
  integrity sha1-zMqrdYr1Z2Hre/N69vA/Mm3XmK0=

"@types/body-parser@*":
  version "1.19.5"
  resolved "http://r.npm.sankuai.com/@types/body-parser/download/@types/body-parser-1.19.5.tgz"
  integrity sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  version "3.5.13"
  resolved "http://r.npm.sankuai.com/@types/bonjour/download/@types/bonjour-3.5.13.tgz"
  integrity sha1-rfkM4aEF6B3R+cYf3Fr9ob+5KVY=
  dependencies:
    "@types/node" "*"

"@types/codemirror@^5.60.10":
  version "5.60.15"
  resolved "http://r.npm.sankuai.com/@types/codemirror/download/@types/codemirror-5.60.15.tgz"
  integrity sha1-D4K+b0Em0eWc9MSDDlbc1J08Poo=
  dependencies:
    "@types/tern" "*"

"@types/connect-history-api-fallback@^1.3.5":
  version "1.5.4"
  resolved "http://r.npm.sankuai.com/@types/connect-history-api-fallback/download/@types/connect-history-api-fallback-1.5.4.tgz"
  integrity sha1-fecWRaEDBWtIrDzgezUguBnB1bM=
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "http://r.npm.sankuai.com/@types/connect/download/@types/connect-3.4.38.tgz"
  integrity sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.7":
  version "3.7.7"
  resolved "http://r.npm.sankuai.com/@types/eslint-scope/download/@types/eslint-scope-3.7.7.tgz"
  integrity sha1-MQi9XxiwzbJ3yGez3UScntcHmsU=
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "9.6.1"
  resolved "http://r.npm.sankuai.com/@types/eslint/download/@types/eslint-9.6.1.tgz"
  integrity sha1-1Xla1zLOgXFfJ/ddqRMASlZ1FYQ=
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/eslint@^7.29.0 || ^8.4.1":
  version "8.56.12"
  resolved "http://r.npm.sankuai.com/@types/eslint/download/@types/eslint-8.56.12.tgz"
  integrity sha1-FlfIFP/rpNL4TA1LoPRMp+ocpTo=
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.6":
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.7.tgz"
  integrity sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^5.0.0":
  version "5.0.6"
  resolved "http://r.npm.sankuai.com/@types/express-serve-static-core/download/@types/express-serve-static-core-5.0.6.tgz"
  integrity sha1-Qf7E6iDpx7IvAkq4ipXGuyiPUbg=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express-serve-static-core@^4.17.33":
  version "4.19.6"
  resolved "http://r.npm.sankuai.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.19.6.tgz"
  integrity sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/@types/express/download/@types/express-5.0.1.tgz"
  integrity sha1-E410HG5duMwnO+xShc1unQd5/J8=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^5.0.0"
    "@types/serve-static" "*"

"@types/express@^4.17.13":
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/@types/express/download/@types/express-4.17.21.tgz"
  integrity sha1-wm1KFR5g7+AISyPcM2nrxjHtGS0=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/html-minifier-terser@^6.0.0":
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/@types/html-minifier-terser/download/@types/html-minifier-terser-6.1.0.tgz"
  integrity sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU=

"@types/http-errors@*":
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/@types/http-errors/download/@types/http-errors-2.0.4.tgz"
  integrity sha1-frR3JsORtzRabsNa1/TeRpz1uk8=

"@types/http-proxy@^1.17.8":
  version "1.17.16"
  resolved "http://r.npm.sankuai.com/@types/http-proxy/download/@types/http-proxy-1.17.16.tgz"
  integrity sha1-3uNgcHs1s8yFr83on/7r/31/kkA=
  dependencies:
    "@types/node" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.15", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/marked@^4.0.7":
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/@types/marked/download/@types/marked-4.3.2.tgz"
  integrity sha1-4uCtAuv1YmvSFcW64q/2r/DOnqw=

"@types/mime@^1":
  version "1.3.5"
  resolved "http://r.npm.sankuai.com/@types/mime/download/@types/mime-1.3.5.tgz"
  integrity sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=

"@types/minimist@^1.2.0":
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/@types/minimist/download/@types/minimist-1.2.5.tgz"
  integrity sha1-7BB1XocUl7zYPv6SfkPsRujAdH4=

"@types/node-fetch@^2.6.4":
  version "2.6.12"
  resolved "http://r.npm.sankuai.com/@types/node-fetch/download/@types/node-fetch-2.6.12.tgz"
  integrity sha1-irXD74Mw8TEAp0eeLNVtM4aDCgM=
  dependencies:
    "@types/node" "*"
    form-data "^4.0.0"

"@types/node-forge@^1.3.0":
  version "1.3.11"
  resolved "http://r.npm.sankuai.com/@types/node-forge/download/@types/node-forge-1.3.11.tgz"
  integrity sha1-CXLqU43bD02cL6DsXbVyR3OmBNo=
  dependencies:
    "@types/node" "*"

"@types/node@*":
  version "22.15.18"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-22.15.18.tgz"
  integrity sha1-L4JA9+ky9XHC1F9VW6C2w/enWWM=
  dependencies:
    undici-types "~6.21.0"

"@types/node@^18.11.18":
  version "18.19.100"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-18.19.100.tgz"
  integrity sha1-fzrvu2kRCZq34JAqHzc7Gk0sGUc=
  dependencies:
    undici-types "~5.26.4"

"@types/normalize-package-data@^2.4.0":
  version "2.4.4"
  resolved "http://r.npm.sankuai.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.4.tgz"
  integrity sha1-VuLMJsOXwDj6sOOpF6EtXFkJ6QE=

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/@types/parse-json/download/@types/parse-json-4.0.2.tgz"
  integrity sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=

"@types/qs@*":
  version "6.9.18"
  resolved "http://r.npm.sankuai.com/@types/qs/download/@types/qs-6.9.18.tgz"
  integrity sha1-h3KSyqkffBshMDKzRiZQW3RmJMI=

"@types/range-parser@*":
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/@types/range-parser/download/@types/range-parser-1.2.7.tgz"
  integrity sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=

"@types/retry@0.12.0":
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/@types/retry/download/@types/retry-0.12.0.tgz"
  integrity sha1-KzXsz87n04zXKtmSMvvVi/+zyE0=

"@types/send@*":
  version "0.17.4"
  resolved "http://r.npm.sankuai.com/@types/send/download/@types/send-0.17.4.tgz"
  integrity sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-index@^1.9.1":
  version "1.9.4"
  resolved "http://r.npm.sankuai.com/@types/serve-index/download/@types/serve-index-1.9.4.tgz"
  integrity sha1-5q4T1QU8sG7TY5IRC0+aSaxOyJg=
  dependencies:
    "@types/express" "*"

"@types/serve-static@*", "@types/serve-static@^1.13.10":
  version "1.15.7"
  resolved "http://r.npm.sankuai.com/@types/serve-static/download/@types/serve-static-1.15.7.tgz"
  integrity sha1-IhdLvXT7l/4wMQlzjptcLzBk9xQ=
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/sockjs@^0.3.33":
  version "0.3.36"
  resolved "http://r.npm.sankuai.com/@types/sockjs/download/@types/sockjs-0.3.36.tgz"
  integrity sha1-zjIs8HvMEZ1Mv3+IlU86O9D2dTU=
  dependencies:
    "@types/node" "*"

"@types/tern@*":
  version "0.23.9"
  resolved "http://r.npm.sankuai.com/@types/tern/download/@types/tern-0.23.9.tgz"
  integrity sha1-b2CTpKmvPmu43eUo4CSSTRlrNnw=
  dependencies:
    "@types/estree" "*"

"@types/ws@^8.5.5":
  version "8.18.1"
  resolved "http://r.npm.sankuai.com/@types/ws/download/@types/ws-8.18.1.tgz"
  integrity sha1-SEZOS/Ld/RfbE9hFRn9gcP/qSqk=
  dependencies:
    "@types/node" "*"

"@vue/babel-helper-vue-jsx-merge-props@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.4.0.tgz"
  integrity sha1-jVOh4hNH247b5U0zmQJYMXbeCfI=

"@vue/babel-helper-vue-transform-on@1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-helper-vue-transform-on/download/@vue/babel-helper-vue-transform-on-1.4.0.tgz"
  integrity sha1-YWAgSIaSqcQqYTKA1i7RtycEXZU=

"@vue/babel-plugin-jsx@^1.0.3":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-plugin-jsx/download/@vue/babel-plugin-jsx-1.4.0.tgz"
  integrity sha1-wVXHlc6YDt9Gqm/s7tk5Ralcplg=
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.26.5"
    "@babel/plugin-syntax-jsx" "^7.25.9"
    "@babel/template" "^7.26.9"
    "@babel/traverse" "^7.26.9"
    "@babel/types" "^7.26.9"
    "@vue/babel-helper-vue-transform-on" "1.4.0"
    "@vue/babel-plugin-resolve-type" "1.4.0"
    "@vue/shared" "^3.5.13"

"@vue/babel-plugin-resolve-type@1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-plugin-resolve-type/download/@vue/babel-plugin-resolve-type-1.4.0.tgz"
  integrity sha1-TTV6gfsMycrQ6MgbEYEVvaLFFUM=
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.26.5"
    "@babel/parser" "^7.26.9"
    "@vue/compiler-sfc" "^3.5.13"

"@vue/babel-plugin-transform-vue-jsx@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.4.0.tgz"
  integrity sha1-TUs9RqOepit0Z91uJs5H986vsv4=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    html-tags "^2.0.0"
    lodash.kebabcase "^4.1.1"
    svg-tags "^1.0.0"

"@vue/babel-preset-app@^5.0.8":
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/@vue/babel-preset-app/download/@vue/babel-preset-app-5.0.8.tgz"
  integrity sha1-zjj3YxT1Jl1iqJdW7yZMIfHTUaE=
  dependencies:
    "@babel/core" "^7.12.16"
    "@babel/helper-compilation-targets" "^7.12.16"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/plugin-proposal-class-properties" "^7.12.13"
    "@babel/plugin-proposal-decorators" "^7.12.13"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.12.13"
    "@babel/plugin-transform-runtime" "^7.12.15"
    "@babel/preset-env" "^7.12.16"
    "@babel/runtime" "^7.12.13"
    "@vue/babel-plugin-jsx" "^1.0.3"
    "@vue/babel-preset-jsx" "^1.1.2"
    babel-plugin-dynamic-import-node "^2.3.3"
    core-js "^3.8.3"
    core-js-compat "^3.8.3"
    semver "^7.3.4"

"@vue/babel-preset-jsx@^1.1.2":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.4.0.tgz"
  integrity sha1-9JFLoxQjWrCXvENy7WdHPAeAv8w=
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "@vue/babel-sugar-composition-api-inject-h" "^1.4.0"
    "@vue/babel-sugar-composition-api-render-instance" "^1.4.0"
    "@vue/babel-sugar-functional-vue" "^1.4.0"
    "@vue/babel-sugar-inject-h" "^1.4.0"
    "@vue/babel-sugar-v-model" "^1.4.0"
    "@vue/babel-sugar-v-on" "^1.4.0"

"@vue/babel-sugar-composition-api-inject-h@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-composition-api-inject-h/download/@vue/babel-sugar-composition-api-inject-h-1.4.0.tgz"
  integrity sha1-GH4TifiHHYns50O7UK7XE76dbIU=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-composition-api-render-instance/download/@vue/babel-sugar-composition-api-render-instance-1.4.0.tgz"
  integrity sha1-LBYHrm3/2rR+eFvAH6Rbp1bpksE=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.4.0.tgz"
  integrity sha1-YNoxBoVnCCKHxzN8Zu9N8E4KECk=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.4.0.tgz"
  integrity sha1-vzmqZjH7HQOZscSbTFnhyImbQ2M=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.4.0.tgz"
  integrity sha1-pR2YZgn0MMT3Cto6k8xWCilw9yA=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    camelcase "^5.0.0"
    html-tags "^2.0.0"
    svg-tags "^1.0.0"

"@vue/babel-sugar-v-on@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.4.0.tgz"
  integrity sha1-Q7cQapZy2Mvu/A64r+HTdu3GFm4=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    camelcase "^5.0.0"

"@vue/cli-overlay@^5.0.8":
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/@vue/cli-overlay/download/@vue/cli-overlay-5.0.8.tgz"
  integrity sha1-thR3rNxDu9QvzmMm0ihHEgHs3N0=

"@vue/cli-plugin-babel@~5.0.0":
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-5.0.8.tgz"
  integrity sha1-VPmgeQDym6/1SAPc+pFsYCiU/rc=
  dependencies:
    "@babel/core" "^7.12.16"
    "@vue/babel-preset-app" "^5.0.8"
    "@vue/cli-shared-utils" "^5.0.8"
    babel-loader "^8.2.2"
    thread-loader "^3.0.0"
    webpack "^5.54.0"

"@vue/cli-plugin-eslint@~5.0.0":
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-5.0.8.tgz"
  integrity sha1-dUk5JlwsW3Rvo2x9dwWokTjhk78=
  dependencies:
    "@vue/cli-shared-utils" "^5.0.8"
    eslint-webpack-plugin "^3.1.0"
    globby "^11.0.2"
    webpack "^5.54.0"
    yorkie "^2.0.0"

"@vue/cli-plugin-router@^5.0.8":
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/@vue/cli-plugin-router/download/@vue/cli-plugin-router-5.0.8.tgz"
  integrity sha1-oRPsYm89QhbSBJbELTVTO86eiJ8=
  dependencies:
    "@vue/cli-shared-utils" "^5.0.8"

"@vue/cli-plugin-vuex@^5.0.8":
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/@vue/cli-plugin-vuex/download/@vue/cli-plugin-vuex-5.0.8.tgz"
  integrity sha1-DUyzAg+RAr6pKI11Bynd4XbGbM0=

"@vue/cli-service@~5.0.0":
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/@vue/cli-service/download/@vue/cli-service-5.0.8.tgz"
  integrity sha1-zz9vG3vw+6nNq4a2vsT5iX+YLaw=
  dependencies:
    "@babel/helper-compilation-targets" "^7.12.16"
    "@soda/friendly-errors-webpack-plugin" "^1.8.0"
    "@soda/get-current-script" "^1.0.2"
    "@types/minimist" "^1.2.0"
    "@vue/cli-overlay" "^5.0.8"
    "@vue/cli-plugin-router" "^5.0.8"
    "@vue/cli-plugin-vuex" "^5.0.8"
    "@vue/cli-shared-utils" "^5.0.8"
    "@vue/component-compiler-utils" "^3.3.0"
    "@vue/vue-loader-v15" "npm:vue-loader@^15.9.7"
    "@vue/web-component-wrapper" "^1.3.0"
    acorn "^8.0.5"
    acorn-walk "^8.0.2"
    address "^1.1.2"
    autoprefixer "^10.2.4"
    browserslist "^4.16.3"
    case-sensitive-paths-webpack-plugin "^2.3.0"
    cli-highlight "^2.1.10"
    clipboardy "^2.3.0"
    cliui "^7.0.4"
    copy-webpack-plugin "^9.0.1"
    css-loader "^6.5.0"
    css-minimizer-webpack-plugin "^3.0.2"
    cssnano "^5.0.0"
    debug "^4.1.1"
    default-gateway "^6.0.3"
    dotenv "^10.0.0"
    dotenv-expand "^5.1.0"
    fs-extra "^9.1.0"
    globby "^11.0.2"
    hash-sum "^2.0.0"
    html-webpack-plugin "^5.1.0"
    is-file-esm "^1.0.0"
    launch-editor-middleware "^2.2.1"
    lodash.defaultsdeep "^4.6.1"
    lodash.mapvalues "^4.6.0"
    mini-css-extract-plugin "^2.5.3"
    minimist "^1.2.5"
    module-alias "^2.2.2"
    portfinder "^1.0.26"
    postcss "^8.2.6"
    postcss-loader "^6.1.1"
    progress-webpack-plugin "^1.0.12"
    ssri "^8.0.1"
    terser-webpack-plugin "^5.1.1"
    thread-loader "^3.0.0"
    vue-loader "^17.0.0"
    vue-style-loader "^4.1.3"
    webpack "^5.54.0"
    webpack-bundle-analyzer "^4.4.0"
    webpack-chain "^6.5.1"
    webpack-dev-server "^4.7.3"
    webpack-merge "^5.7.3"
    webpack-virtual-modules "^0.4.2"
    whatwg-fetch "^3.6.2"

"@vue/cli-shared-utils@^5.0.8":
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/@vue/cli-shared-utils/download/@vue/cli-shared-utils-5.0.8.tgz"
  integrity sha1-dfyWUo66Kxx+M8t+mJqYTd75nIo=
  dependencies:
    "@achrinza/node-ipc" "^9.2.5"
    chalk "^4.1.2"
    execa "^1.0.0"
    joi "^17.4.0"
    launch-editor "^2.2.1"
    lru-cache "^6.0.0"
    node-fetch "^2.6.7"
    open "^8.0.2"
    ora "^5.3.0"
    read-pkg "^5.1.1"
    semver "^7.3.4"
    strip-ansi "^6.0.0"

"@vue/compiler-core@3.5.14":
  version "3.5.14"
  resolved "http://r.npm.sankuai.com/@vue/compiler-core/download/@vue/compiler-core-3.5.14.tgz"
  integrity sha1-NnZoXATEiltKVRWzsoQumDQsVVw=
  dependencies:
    "@babel/parser" "^7.27.2"
    "@vue/shared" "3.5.14"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.1"

"@vue/compiler-dom@3.5.14":
  version "3.5.14"
  resolved "http://r.npm.sankuai.com/@vue/compiler-dom/download/@vue/compiler-dom-3.5.14.tgz"
  integrity sha1-u/J0IfgPe4hzAA7c7s2BfEq/Q4o=
  dependencies:
    "@vue/compiler-core" "3.5.14"
    "@vue/shared" "3.5.14"

"@vue/compiler-sfc@2.7.16":
  version "2.7.16"
  resolved "http://r.npm.sankuai.com/@vue/compiler-sfc/download/@vue/compiler-sfc-2.7.16.tgz"
  integrity sha1-/4FxGg+snGhoPYuwC2P4V9533IM=
  dependencies:
    "@babel/parser" "^7.23.5"
    postcss "^8.4.14"
    source-map "^0.6.1"
  optionalDependencies:
    prettier "^1.18.2 || ^2.0.0"

"@vue/compiler-sfc@^3.5.13":
  version "3.5.14"
  resolved "http://r.npm.sankuai.com/@vue/compiler-sfc/download/@vue/compiler-sfc-3.5.14.tgz"
  integrity sha1-/D2zChx0QTnUG7V7tFHXg0Ffzks=
  dependencies:
    "@babel/parser" "^7.27.2"
    "@vue/compiler-core" "3.5.14"
    "@vue/compiler-dom" "3.5.14"
    "@vue/compiler-ssr" "3.5.14"
    "@vue/shared" "3.5.14"
    estree-walker "^2.0.2"
    magic-string "^0.30.17"
    postcss "^8.5.3"
    source-map-js "^1.2.1"

"@vue/compiler-ssr@3.5.14":
  version "3.5.14"
  resolved "http://r.npm.sankuai.com/@vue/compiler-ssr/download/@vue/compiler-ssr-3.5.14.tgz"
  integrity sha1-ATF07mu/PuKRpt8kej/rbrQ9gIs=
  dependencies:
    "@vue/compiler-dom" "3.5.14"
    "@vue/shared" "3.5.14"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.3.0":
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.3.0.tgz"
  integrity sha1-+fX7U0ZLDDeyyNLz+/5E32D2Hck=
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.36"
    postcss-selector-parser "^6.0.2"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"
  optionalDependencies:
    prettier "^1.18.2 || ^2.0.0"

"@vue/shared@3.5.14", "@vue/shared@^3.5.13":
  version "3.5.14"
  resolved "http://r.npm.sankuai.com/@vue/shared/download/@vue/shared-3.5.14.tgz"
  integrity sha1-j83GxpZhoRY8FzyvthKcP4rQESI=

"@vue/vue-loader-v15@npm:vue-loader@^15.9.7":
  version "15.11.1"
  resolved "http://r.npm.sankuai.com/vue-loader/download/vue-loader-15.11.1.tgz"
  integrity sha1-3ukRaSESdu1DxXFcrviKVrH0l7A=
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    vue-hot-reload-api "^2.3.0"
    vue-style-loader "^4.1.0"

"@vue/web-component-wrapper@^1.3.0":
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.3.0.tgz"
  integrity sha1-trQKdiVCnSvXwigd26YB7QXcfxo=

"@webassemblyjs/ast@1.14.1", "@webassemblyjs/ast@^1.14.1":
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.14.1.tgz"
  integrity sha1-qfagfysDyVyNOMRTah/ftSH/VbY=
  dependencies:
    "@webassemblyjs/helper-numbers" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"

"@webassemblyjs/floating-point-hex-parser@1.13.2":
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.13.2.tgz"
  integrity sha1-/Moe7dscxOe27tT8eVbWgTshufs=

"@webassemblyjs/helper-api-error@1.13.2":
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.13.2.tgz"
  integrity sha1-4KFhUiSLw42u523X4h8Vxe86sec=

"@webassemblyjs/helper-buffer@1.14.1":
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.14.1.tgz"
  integrity sha1-giqbxgMWZTH31d+E5ntb+ZtyuWs=

"@webassemblyjs/helper-numbers@1.13.2":
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.13.2.tgz"
  integrity sha1-29kyVI5xGfS4p4d/1ajSDmNJCy0=
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.13.2"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.13.2":
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.13.2.tgz"
  integrity sha1-5VYQh1j0SKroTIUOWTzhig6zHgs=

"@webassemblyjs/helper-wasm-section@1.14.1":
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.14.1.tgz"
  integrity sha1-lindqcRDDqtUtZEFPW3G87oFA0g=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/wasm-gen" "1.14.1"

"@webassemblyjs/ieee754@1.13.2":
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.13.2.tgz"
  integrity sha1-HF6qzh1gatosf9cEXqk1bFnuDbo=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.13.2":
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.13.2.tgz"
  integrity sha1-V8XD3rAQXQLOJfo/109OvJ/Qu7A=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.13.2":
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.13.2.tgz"
  integrity sha1-kXog6T9xrVYClmwtaFrgxsIfYPE=

"@webassemblyjs/wasm-edit@^1.14.1":
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.14.1.tgz"
  integrity sha1-rGaJ9QIhm1kZjd7ELc1JaxAE1Zc=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/helper-wasm-section" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-opt" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"
    "@webassemblyjs/wast-printer" "1.14.1"

"@webassemblyjs/wasm-gen@1.14.1":
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.14.1.tgz"
  integrity sha1-mR5/DAkMsLtiu6yIIHbj0hnalXA=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-opt@1.14.1":
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.14.1.tgz"
  integrity sha1-5vce18yuRngcIGAX08FMUO+oEGs=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"

"@webassemblyjs/wasm-parser@1.14.1", "@webassemblyjs/wasm-parser@^1.14.1":
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.14.1.tgz"
  integrity sha1-s+E/GJNgXKeLUsaOVM9qhl+Qufs=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wast-printer@1.14.1":
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.14.1.tgz"
  integrity sha1-O7PpY4qK5f2vlhDnoGtNn5qm/gc=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@xtuc/long" "4.2.2"

"@xmldom/xmldom@^0.8.6":
  version "0.8.10"
  resolved "http://r.npm.sankuai.com/@xmldom/xmldom/download/@xmldom/xmldom-0.8.10.tgz"
  integrity sha1-oTN8pCaqYc75/hW1so40CnL2+pk=

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "http://r.npm.sankuai.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/abort-controller/download/abort-controller-3.0.0.tgz"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

accepts@~1.3.4, accepts@~1.3.8:
  version "1.3.8"
  resolved "http://r.npm.sankuai.com/accepts/download/accepts-1.3.8.tgz"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-jsx@^5.3.1, acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.0.0, acorn-walk@^8.0.2:
  version "8.3.4"
  resolved "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-8.3.4.tgz"
  integrity sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=
  dependencies:
    acorn "^8.11.0"

acorn@^7.4.0:
  version "7.4.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-7.4.1.tgz"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.0.4, acorn@^8.0.5, acorn@^8.11.0, acorn@^8.14.0, acorn@^8.9.0:
  version "8.14.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.14.1.tgz"
  integrity sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=

address@^1.1.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/address/download/address-1.2.2.tgz"
  integrity sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=

agentkeepalive@^4.2.1:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/agentkeepalive/download/agentkeepalive-4.6.0.tgz"
  integrity sha1-Nfc+lLP0C/ZfEFIZxiOtGcE26mo=
  dependencies:
    humanize-ms "^1.2.1"

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/ajv-formats/download/ajv-formats-2.1.1.tgz"
  integrity sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv-keywords@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-5.1.0.tgz"
  integrity sha1-adTThaRzPNvqtElkoRcKiPh/DhY=
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.10.0, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.0.1, ajv@^8.9.0:
  version "8.17.1"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-8.17.1.tgz"
  integrity sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ansi-colors@^4.1.1:
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-4.1.3.tgz"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

ansi-escapes@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "http://r.npm.sankuai.com/ansi-html-community/download/ansi-html-community-0.0.8.tgz"
  integrity sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.1.tgz"
  integrity sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/any-promise/download/any-promise-1.3.0.tgz"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arch@^2.1.1:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/arch/download/arch-2.2.0.tgz"
  integrity sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE=

argparse@^1.0.7, argparse@~1.0.3:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/array-flatten/download/array-flatten-1.1.1.tgz"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

asn1.js@^4.10.1:
  version "4.10.1"
  resolved "http://r.npm.sankuai.com/asn1.js/download/asn1.js-4.10.1.tgz"
  integrity sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/astral-regex/download/astral-regex-2.0.0.tgz"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

async-validator@~1.8.1:
  version "1.8.5"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-1.8.5.tgz"
  integrity sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A=
  dependencies:
    babel-runtime "6.x"

async@^3.2.6:
  version "3.2.6"
  resolved "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz"
  integrity sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/at-least-node/download/at-least-node-1.0.0.tgz"
  integrity sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=

autoprefixer@^10.2.4:
  version "10.4.21"
  resolved "http://r.npm.sankuai.com/autoprefixer/download/autoprefixer-10.4.21.tgz"
  integrity sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=
  dependencies:
    browserslist "^4.24.4"
    caniuse-lite "^1.0.30001702"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.1.1"
    postcss-value-parser "^4.2.0"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

axios@^1.9.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/axios/download/axios-1.9.0.tgz"
  integrity sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

babel-helper-vue-jsx-merge-props@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  integrity sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY=

babel-loader@^8.2.2:
  version "8.4.1"
  resolved "http://r.npm.sankuai.com/babel-loader/download/babel-loader-8.4.1.tgz"
  integrity sha1-bMt1xm5iw7FE4cXy6uxbj2wIxnU=
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^2.0.4"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz"
  integrity sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-polyfill-corejs2@^0.4.10:
  version "0.4.13"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.13.tgz"
  integrity sha1-fURfDgYH68j7awHX6PsCBpuR3Ys=
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.4"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.11.0:
  version "0.11.1"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.11.1.tgz"
  integrity sha1-Tk4YLxuzfHumLir4HY3QnfMTRPY=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.3"
    core-js-compat "^3.40.0"

babel-plugin-polyfill-regenerator@^0.6.1:
  version "0.6.4"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.6.4.tgz"
  integrity sha1-QoxhXTwXcpKiK0+T7ZnjWNeQaps=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.4"

babel-runtime@6.x:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-runtime/download/babel-runtime-6.26.0.tgz"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.3.1, base64-js@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

batch@0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/batch/download/batch-0.6.1.tgz"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://r.npm.sankuai.com/big.js/download/big.js-5.2.2.tgz"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

bl@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bluebird@^3.1.1:
  version "3.7.2"
  resolved "http://r.npm.sankuai.com/bluebird/download/bluebird-3.7.2.tgz"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bluebird@~3.4.0:
  version "3.4.7"
  resolved "http://r.npm.sankuai.com/bluebird/download/bluebird-3.4.7.tgz"
  integrity sha1-9y12C+Cbf3bQjtj66Ysomo0F+rM=

bmp-js@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/bmp-js/download/bmp-js-0.1.0.tgz"
  integrity sha1-4Fpj95amwf8l9Hcex62twUjAcjM=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.11.9:
  version "4.12.2"
  resolved "http://r.npm.sankuai.com/bn.js/download/bn.js-4.12.2.tgz"
  integrity sha1-PY/tZ5bCThd3N/fMUXLuBO857Jk=

bn.js@^5.2.1:
  version "5.2.2"
  resolved "http://r.npm.sankuai.com/bn.js/download/bn.js-5.2.2.tgz"
  integrity sha1-gsCfnruxcQfNcst/05vR+dCqpWY=

body-parser@1.20.3:
  version "1.20.3"
  resolved "http://r.npm.sankuai.com/body-parser/download/body-parser-1.20.3.tgz"
  integrity sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.13.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour-service@^1.0.11:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/bonjour-service/download/bonjour-service-1.3.0.tgz"
  integrity sha1-gNhnQwtaDaZOgqgEf8HjVb23FyI=
  dependencies:
    fast-deep-equal "^3.1.3"
    multicast-dns "^7.2.5"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

brorand@^1.0.1, brorand@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/brorand/download/brorand-1.1.0.tgz"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browser-image-compression@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/browser-image-compression/download/browser-image-compression-2.0.2.tgz"
  integrity sha1-TV74iC6eRx1tkjcVzrkDRJnRTqo=
  dependencies:
    uzip "0.20201231.0"

browserify-aes@^1.0.4, browserify-aes@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/browserify-aes/download/browserify-aes-1.2.0.tgz"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/browserify-des/download/browserify-des-1.0.2.tgz"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.1.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/browserify-rsa/download/browserify-rsa-4.1.1.tgz"
  integrity sha1-BuUwkH/ilJ3CH8PC4jAuELFDcjg=
  dependencies:
    bn.js "^5.2.1"
    randombytes "^2.1.0"
    safe-buffer "^5.2.1"

browserify-sign@^4.2.3:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/browserify-sign/download/browserify-sign-4.2.3.tgz"
  integrity sha1-ev5MAex+5ZqJpVikt1vYWuYtQgg=
  dependencies:
    bn.js "^5.2.1"
    browserify-rsa "^4.1.0"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.5"
    hash-base "~3.0"
    inherits "^2.0.4"
    parse-asn1 "^5.1.7"
    readable-stream "^2.3.8"
    safe-buffer "^5.2.1"

browserslist@^4.0.0, browserslist@^4.16.3, browserslist@^4.21.4, browserslist@^4.24.0, browserslist@^4.24.4:
  version "4.24.5"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.24.5.tgz"
  integrity sha1-qg9bhWD+gf3oTG3LOPdZuvug4Rs=
  dependencies:
    caniuse-lite "^1.0.30001716"
    electron-to-chromium "^1.5.149"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/buffer-xor/download/buffer-xor-1.0.3.tgz"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^5.5.0:
  version "5.7.1"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^6.0.3:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-6.0.3.tgz"
  integrity sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

bytes@3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/bytes/download/bytes-3.1.2.tgz"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.8.tgz"
  integrity sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz"
  integrity sha1-I43pNdKippKSjFOMfM+pEGf9Bio=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/camel-case/download/camel-case-4.1.2.tgz"
  integrity sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase@^5.0.0:
  version "5.3.1"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/caniuse-api/download/caniuse-api-3.0.0.tgz"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001702, caniuse-lite@^1.0.30001716:
  version "1.0.30001718"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001718.tgz"
  integrity sha1-2uE6nIDVF8MMYZdRWpYTHBlNj4I=

case-sensitive-paths-webpack-plugin@^2.3.0:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  integrity sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ=

chalk@^2.1.0, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-3.0.0.tgz"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chokidar@^3.5.3:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-3.6.0.tgz"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chrome-trace-event@^1.0.2:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/chrome-trace-event/download/chrome-trace-event-1.0.4.tgz"
  integrity sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=

ci-info@^1.5.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/ci-info/download/ci-info-1.6.0.tgz"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/cipher-base/download/cipher-base-1.0.6.tgz"
  integrity sha1-j+ZyQ30BzWxFYa9TNODMUP8ZVfc=
  dependencies:
    inherits "^2.0.4"
    safe-buffer "^5.2.1"

clean-css@^5.2.2:
  version "5.3.3"
  resolved "http://r.npm.sankuai.com/clean-css/download/clean-css-5.3.3.tgz"
  integrity sha1-szBlPNO9a3UAnMJccUyue5M1HM0=
  dependencies:
    source-map "~0.6.0"

cli-cursor@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.1.10:
  version "2.1.11"
  resolved "http://r.npm.sankuai.com/cli-highlight/download/cli-highlight-2.1.11.tgz"
  integrity sha1-SXNvpFLwqvT65YDjCssmgo0twb8=
  dependencies:
    chalk "^4.0.0"
    highlight.js "^10.7.1"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^6.0.0"
    yargs "^16.0.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz"
  integrity sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=

clipboardy@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/clipboardy/download/clipboardy-2.3.0.tgz"
  integrity sha1-PCkDZQxo5GqRs4iYW8J3QofbopA=
  dependencies:
    arch "^2.1.1"
    execa "^1.0.0"
    is-wsl "^2.1.1"

cliui@^7.0.2, cliui@^7.0.4:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-7.0.4.tgz"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/clone-deep/download/clone-deep-4.0.1.tgz"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

codemirror-spell-checker@1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/codemirror-spell-checker/download/codemirror-spell-checker-1.1.2.tgz"
  integrity sha1-HGYPkIlIPMtRE7m6nKGcP0mTNx4=
  dependencies:
    typo-js "*"

codemirror@^5.65.15:
  version "5.65.19"
  resolved "http://r.npm.sankuai.com/codemirror/download/codemirror-5.65.19.tgz"
  integrity sha1-cQFscB1qS24ZgrD25xhr5l5JZT0=

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colord@^2.9.1:
  version "2.9.3"
  resolved "http://r.npm.sankuai.com/colord/download/colord-2.9.3.tgz"
  integrity sha1-T4zpGd5Fbx1cHDaMMH/iDz5Z+0M=

colorette@^2.0.10:
  version "2.0.20"
  resolved "http://r.npm.sankuai.com/colorette/download/colorette-2.0.20.tgz"
  integrity sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.20.0:
  version "2.20.3"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^7.2.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-7.2.0.tgz"
  integrity sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=

commander@^8.3.0:
  version "8.3.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-8.3.0.tgz"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

commondir@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/commondir/download/commondir-1.0.1.tgz"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

compressible@~2.0.18:
  version "2.0.18"
  resolved "http://r.npm.sankuai.com/compressible/download/compressible-2.0.18.tgz"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/compression/download/compression-1.8.0.tgz"
  integrity sha1-CUIO/JbhGg9E86VY3lnjITZBgPc=
  dependencies:
    bytes "3.1.2"
    compressible "~2.0.18"
    debug "2.6.9"
    negotiator "~0.6.4"
    on-headers "~1.0.2"
    safe-buffer "5.2.1"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

connect-history-api-fallback@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/connect-history-api-fallback/download/connect-history-api-fallback-2.0.0.tgz"
  integrity sha1-ZHJkhFJRoNryW5fOh4NMrOD18cg=

consolidate@^0.15.1:
  version "0.15.1"
  resolved "http://r.npm.sankuai.com/consolidate/download/consolidate-0.15.1.tgz"
  integrity sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=
  dependencies:
    bluebird "^3.1.1"

content-disposition@0.5.4:
  version "0.5.4"
  resolved "http://r.npm.sankuai.com/content-disposition/download/content-disposition-0.5.4.tgz"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/content-type/download/content-type-1.0.5.tgz"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/cookie-signature/download/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.7.1:
  version "0.7.1"
  resolved "http://r.npm.sankuai.com/cookie/download/cookie-0.7.1.tgz"
  integrity sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=

copy-webpack-plugin@^9.0.1:
  version "9.1.0"
  resolved "http://r.npm.sankuai.com/copy-webpack-plugin/download/copy-webpack-plugin-9.1.0.tgz"
  integrity sha1-LSxGDExGlewKWK+ygBoSBSVsTms=
  dependencies:
    fast-glob "^3.2.7"
    glob-parent "^6.0.1"
    globby "^11.0.3"
    normalize-path "^3.0.0"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.0"

core-js-compat@^3.40.0, core-js-compat@^3.8.3:
  version "3.42.0"
  resolved "http://r.npm.sankuai.com/core-js-compat/download/core-js-compat-3.42.0.tgz"
  integrity sha1-zhnClwbuWAbibTyzxULUz8DtUbs=
  dependencies:
    browserslist "^4.24.4"

core-js@^2.4.0:
  version "2.6.12"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-2.6.12.tgz"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-js@^3.8.3:
  version "3.42.0"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.42.0.tgz"
  integrity sha1-7b6R94rIz7bfjZl+dNNopoCC/jc=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-7.1.0.tgz"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

create-ecdh@^4.0.4:
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/create-ecdh/download/create-ecdh-4.0.4.tgz"
  integrity sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/create-hash/download/create-hash-1.2.0.tgz"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/create-hmac/download/create-hmac-1.1.7.tgz"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-5.1.0.tgz"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0:
  version "6.0.6"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-6.0.6.tgz"
  integrity sha1-MNDvoHEt2361p24ehyG/+vprXVc=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-browserify@^3.12.0:
  version "3.12.1"
  resolved "http://r.npm.sankuai.com/crypto-browserify/download/crypto-browserify-3.12.1.tgz"
  integrity sha1-u4khvsmsyBYzN5qo9S1psLaeDaw=
  dependencies:
    browserify-cipher "^1.0.1"
    browserify-sign "^4.2.3"
    create-ecdh "^4.0.4"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    diffie-hellman "^5.0.3"
    hash-base "~3.0.4"
    inherits "^2.0.4"
    pbkdf2 "^3.1.2"
    public-encrypt "^4.0.3"
    randombytes "^2.1.0"
    randomfill "^1.0.4"

css-declaration-sorter@^6.3.1:
  version "6.4.1"
  resolved "http://r.npm.sankuai.com/css-declaration-sorter/download/css-declaration-sorter-6.4.1.tgz"
  integrity sha1-KL6sfCC61/F3W+OnEp1+rkCaOnE=

css-loader@^6.5.0:
  version "6.11.0"
  resolved "http://r.npm.sankuai.com/css-loader/download/css-loader-6.11.0.tgz"
  integrity sha1-M7rjv2Nj0KfCz5AxyWx0T/VNhbo=
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.4.33"
    postcss-modules-extract-imports "^3.1.0"
    postcss-modules-local-by-default "^4.0.5"
    postcss-modules-scope "^3.2.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.2.0"
    semver "^7.5.4"

css-minimizer-webpack-plugin@^3.0.2:
  version "3.4.1"
  resolved "http://r.npm.sankuai.com/css-minimizer-webpack-plugin/download/css-minimizer-webpack-plugin-3.4.1.tgz"
  integrity sha1-q3j3gc7ZGBmS/ntuTzQi52Qph48=
  dependencies:
    cssnano "^5.0.6"
    jest-worker "^27.0.2"
    postcss "^8.3.5"
    schema-utils "^4.0.0"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"

css-select@^4.1.3:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/css-select/download/css-select-4.3.0.tgz"
  integrity sha1-23EpsoRmYv2GKM/ElquytZ5BUps=
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.0.1"
    domhandler "^4.3.1"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-tree@^1.1.2, css-tree@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/css-tree/download/css-tree-1.1.3.tgz"
  integrity sha1-60hw+2/XcHMn7JXC/yqwm16NuR0=
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-what@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/css-what/download/css-what-6.1.0.tgz"
  integrity sha1-+17/z3bx3eosgb36pN5E55uscPQ=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cssesc/download/cssesc-3.0.0.tgz"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano-preset-default@^5.2.14:
  version "5.2.14"
  resolved "http://r.npm.sankuai.com/cssnano-preset-default/download/cssnano-preset-default-5.2.14.tgz"
  integrity sha1-MJ3vT3t+FtcaskOAUgkzMNmrRdg=
  dependencies:
    css-declaration-sorter "^6.3.1"
    cssnano-utils "^3.1.0"
    postcss-calc "^8.2.3"
    postcss-colormin "^5.3.1"
    postcss-convert-values "^5.1.3"
    postcss-discard-comments "^5.1.2"
    postcss-discard-duplicates "^5.1.0"
    postcss-discard-empty "^5.1.1"
    postcss-discard-overridden "^5.1.0"
    postcss-merge-longhand "^5.1.7"
    postcss-merge-rules "^5.1.4"
    postcss-minify-font-values "^5.1.0"
    postcss-minify-gradients "^5.1.1"
    postcss-minify-params "^5.1.4"
    postcss-minify-selectors "^5.2.1"
    postcss-normalize-charset "^5.1.0"
    postcss-normalize-display-values "^5.1.0"
    postcss-normalize-positions "^5.1.1"
    postcss-normalize-repeat-style "^5.1.1"
    postcss-normalize-string "^5.1.0"
    postcss-normalize-timing-functions "^5.1.0"
    postcss-normalize-unicode "^5.1.1"
    postcss-normalize-url "^5.1.0"
    postcss-normalize-whitespace "^5.1.1"
    postcss-ordered-values "^5.1.3"
    postcss-reduce-initial "^5.1.2"
    postcss-reduce-transforms "^5.1.0"
    postcss-svgo "^5.1.0"
    postcss-unique-selectors "^5.1.1"

cssnano-utils@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cssnano-utils/download/cssnano-utils-3.1.0.tgz"
  integrity sha1-lWhNCMkVEe38cNJjYzjKN+86aGE=

cssnano@^5.0.0, cssnano@^5.0.6:
  version "5.1.15"
  resolved "http://r.npm.sankuai.com/cssnano/download/cssnano-5.1.15.tgz"
  integrity sha1-3tZrVIDVEn/LRNrBLqWpg3VRNr8=
  dependencies:
    cssnano-preset-default "^5.2.14"
    lilconfig "^2.0.3"
    yaml "^1.10.2"

csso@^4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/csso/download/csso-4.2.0.tgz"
  integrity sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=
  dependencies:
    css-tree "^1.1.2"

csstype@^3.1.0:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

de-indent@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/de-indent/download/de-indent-1.0.2.tgz"
  integrity sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=

debounce@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/debounce/download/debounce-1.2.1.tgz"
  integrity sha1-OIgdj0FmpcWEgCDBGCe4NLyz4KU=

debug@2.6.9:
  version "2.6.9"
  resolved "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.6:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^1.2.0, deepmerge@^1.5.2:
  version "1.5.2"
  resolved "http://r.npm.sankuai.com/deepmerge/download/deepmerge-1.5.2.tgz"
  integrity sha1-EEmdhohEza1P7ghC34x/bwyVp1M=

default-gateway@^6.0.3:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/default-gateway/download/default-gateway-6.0.3.tgz"
  integrity sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=
  dependencies:
    execa "^5.0.0"

defaults@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/define-lazy-prop/download/define-lazy-prop-2.0.0.tgz"
  integrity sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=

define-properties@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

depd@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

depd@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/depd/download/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/des.js/download/des.js-1.1.0.tgz"
  integrity sha1-HTf1dm87v/Tuljjocah2jBc7gdo=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/destroy/download/destroy-1.2.0.tgz"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-node@^2.0.4:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/detect-node/download/detect-node-2.1.0.tgz"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

diffie-hellman@^5.0.3:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dingbat-to-unicode@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dingbat-to-unicode/download/dingbat-to-unicode-1.0.1.tgz"
  integrity sha1-UJHdZzJBRT5rWGXiblpEUs3vXIM=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dns-packet@^5.2.2:
  version "5.6.1"
  resolved "http://r.npm.sankuai.com/dns-packet/download/dns-packet-5.6.1.tgz"
  integrity sha1-roiK1CWp0UeKBnQlarhm3hASzy8=
  dependencies:
    "@leichtgewicht/ip-codec" "^2.0.1"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/dom-converter/download/dom-converter-0.2.0.tgz"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-serializer@^1.0.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-1.4.1.tgz"
  integrity sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/domhandler/download/domhandler-4.3.1.tgz"
  integrity sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=
  dependencies:
    domelementtype "^2.2.0"

domutils@^2.5.2, domutils@^2.8.0:
  version "2.8.0"
  resolved "http://r.npm.sankuai.com/domutils/download/domutils-2.8.0.tgz"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/dot-case/download/dot-case-3.0.4.tgz"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz"
  integrity sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=

dotenv@^10.0.0:
  version "10.0.0"
  resolved "http://r.npm.sankuai.com/dotenv/download/dotenv-10.0.0.tgz"
  integrity sha1-PUInuPuV+BCWzdK2ZlP7LHCFuoE=

duck@^0.1.12:
  version "0.1.12"
  resolved "http://r.npm.sankuai.com/duck/download/duck-0.1.12.tgz"
  integrity sha1-3nrfdYQhIwtteu55nOQmcFhrnvo=
  dependencies:
    underscore "^1.13.1"

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

duplexer@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/duplexer/download/duplexer-0.1.2.tgz"
  integrity sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=

easy-stack@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/easy-stack/download/easy-stack-1.0.1.tgz"
  integrity sha1-iv5CZGJpiMq7EfPHBMzQyDVBEGY=

easymde@^2.15.0, easymde@^2.20.0:
  version "2.20.0"
  resolved "http://r.npm.sankuai.com/easymde/download/easymde-2.20.0.tgz"
  integrity sha1-iLMWH+q24ZAK+pxNqz8do1Kwom4=
  dependencies:
    "@types/codemirror" "^5.60.10"
    "@types/marked" "^4.0.7"
    codemirror "^5.65.15"
    codemirror-spell-checker "1.1.2"
    marked "^4.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.5.149:
  version "1.5.155"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.155.tgz"
  integrity sha1-gJ3Qrprh24fDWODAwXwJov/EMtE=

element-ui@^2.15.14:
  version "2.15.14"
  resolved "http://r.npm.sankuai.com/element-ui/download/element-ui-2.15.14.tgz"
  integrity sha1-PDTfeUZ2NlkoEtcg0uZ4Tnpuwuo=
  dependencies:
    async-validator "~1.8.1"
    babel-helper-vue-jsx-merge-props "^2.0.0"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    resize-observer-polyfill "^1.5.0"
    throttle-debounce "^1.0.1"

elliptic@^6.5.3, elliptic@^6.5.5:
  version "6.6.1"
  resolved "http://r.npm.sankuai.com/elliptic/download/elliptic-6.6.1.tgz"
  integrity sha1-O4/7AmcL9p44LH9lv1JMl8VAXAY=
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/emojis-list/download/emojis-list-3.0.0.tgz"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/encodeurl/download/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encodeurl@~2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/encodeurl/download/encodeurl-2.0.0.tgz"
  integrity sha1-e46omAd9fkCdOsRUdOo46vCFelg=

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^5.17.1:
  version "5.18.1"
  resolved "http://r.npm.sankuai.com/enhanced-resolve/download/enhanced-resolve-5.18.1.tgz"
  integrity sha1-coqwgvi3toNt5R8WN6q107lWj68=
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enquirer@^2.3.5:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/enquirer/download/enquirer-2.4.1.tgz"
  integrity sha1-kzNLP710/HCXsiSrSo+35Av0rlY=
  dependencies:
    ansi-colors "^4.1.1"
    strip-ansi "^6.0.1"

entities@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/entities/download/entities-2.2.0.tgz"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

entities@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/error-stack-parser/download/error-stack-parser-2.1.4.tgz"
  integrity sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY=
  dependencies:
    stackframe "^1.3.4"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-module-lexer@^1.2.1:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/es-module-lexer/download/es-module-lexer-1.7.0.tgz"
  integrity sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-plugin-vue@^8.0.3:
  version "8.7.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-vue/download/eslint-plugin-vue-8.7.1.tgz"
  integrity sha1-8TxTVHoMnWRYimdcxezGzK9jcD8=
  dependencies:
    eslint-utils "^3.0.0"
    natural-compare "^1.4.0"
    nth-check "^2.0.1"
    postcss-selector-parser "^6.0.9"
    semver "^7.3.5"
    vue-eslint-parser "^8.0.1"

eslint-scope@5.1.1, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.0.0:
  version "7.2.2"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-2.1.0.tgz"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-3.0.0.tgz"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0, eslint-visitor-keys@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.1.0, eslint-visitor-keys@^3.4.1:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint-webpack-plugin@^3.1.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/eslint-webpack-plugin/download/eslint-webpack-plugin-3.2.0.tgz"
  integrity sha1-GXjNue3EYeSwGVog2pUM9XmINHw=
  dependencies:
    "@types/eslint" "^7.29.0 || ^8.4.1"
    jest-worker "^28.0.2"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    schema-utils "^4.0.0"

eslint@^7.32.0:
  version "7.32.0"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-7.32.0.tgz"
  integrity sha1-xtMooUvj+wjI0dIeEsAv23oqgS0=
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.1.2"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.9"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-7.3.1.tgz"
  integrity sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

espree@^9.0.0:
  version "9.6.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-2.0.2.tgz"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "http://r.npm.sankuai.com/etag/download/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-pubsub@4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/event-pubsub/download/event-pubsub-4.3.0.tgz"
  integrity sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/event-target-shim/download/event-target-shim-5.0.1.tgz"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-4.0.7.tgz"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.2.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/events/download/events-3.3.0.tgz"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^0.8.0:
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-0.8.0.tgz"
  integrity sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-1.0.0.tgz"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/execa/download/execa-5.1.1.tgz"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

express@^4.17.3:
  version "4.21.2"
  resolved "http://r.npm.sankuai.com/express/download/express-4.21.2.tgz"
  integrity sha1-zyUOSDYhdOrWzqSlZqvvAWLB7DI=
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.3"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.7.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.3.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.3"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.12"
    proxy-addr "~2.0.7"
    qs "6.13.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.19.0"
    serve-static "1.16.2"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-glob@^3.2.7, fast-glob@^3.2.9:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.3.tgz"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-uri@^3.0.1:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/fast-uri/download/fast-uri-3.0.6.tgz"
  integrity sha1-iPEwt3z66iN41Wv5cN6iElemh0g=

fastq@^1.6.0:
  version "1.19.1"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.19.1.tgz"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "http://r.npm.sankuai.com/faye-websocket/download/faye-websocket-0.11.4.tgz"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

figures@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-2.0.0.tgz"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/finalhandler/download/finalhandler-1.3.1.tgz"
  integrity sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=
  dependencies:
    debug "2.6.9"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-cache-dir@^3.3.1:
  version "3.3.2"
  resolved "http://r.npm.sankuai.com/find-cache-dir/download/find-cache-dir-3.3.2.tgz"
  integrity sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.2.0.tgz"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flat@^5.0.2:
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/flat/download/flat-5.0.2.tgz"
  integrity sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=

flatted@^3.2.9:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.3.3.tgz"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

follow-redirects@^1.0.0, follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz"
  integrity sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=

for-each@^0.3.5:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/for-each/download/for-each-0.3.5.tgz"
  integrity sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=
  dependencies:
    is-callable "^1.2.7"

form-data-encoder@1.7.2:
  version "1.7.2"
  resolved "http://r.npm.sankuai.com/form-data-encoder/download/form-data-encoder-1.7.2.tgz"
  integrity sha1-Hxrj3M9Y7UaQuG2H5PV8ZU+6sEA=

form-data@^4.0.0:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-4.0.2.tgz"
  integrity sha1-Ncq73TDDznPessQtPI0+2cpReUw=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    mime-types "^2.1.12"

formdata-node@^4.3.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/formdata-node/download/formdata-node-4.4.1.tgz"
  integrity sha1-I/aly5y1UxWRLL7E/3sPWbvRkeI=
  dependencies:
    node-domexception "1.0.0"
    web-streams-polyfill "4.0.0-beta.3"

forwarded@0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/forwarded/download/forwarded-0.2.0.tgz"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "http://r.npm.sankuai.com/fraction.js/download/fraction.js-4.3.7.tgz"
  integrity sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=

fresh@0.5.2:
  version "0.5.2"
  resolved "http://r.npm.sankuai.com/fresh/download/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@^9.1.0:
  version "9.1.0"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-9.1.0.tgz"
  integrity sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/fs-monkey/download/fs-monkey-1.0.6.tgz"
  integrity sha1-jq0IKVPojZks8/+ET6qQeyZ1baI=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stream@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-3.0.0.tgz"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-4.1.0.tgz"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-6.0.1.tgz"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^7.1.3:
  version "7.2.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.6.0, globals@^13.9.0:
  version "13.24.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globby@^11.0.2, globby@^11.0.3:
  version "11.1.0"
  resolved "http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.11, graceful-fs@^4.2.4, graceful-fs@^4.2.6:
  version "4.2.11"
  resolved "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

gzip-size@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/gzip-size/download/gzip-size-6.0.0.tgz"
  integrity sha1-BlNn/VDCOcBnHLy61b4+LusQ5GI=
  dependencies:
    duplexer "^0.1.2"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/handle-thing/download/handle-thing-2.0.1.tgz"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/hash-base/download/hash-base-3.1.0.tgz"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-base@~3.0, hash-base@~3.0.4:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/hash-base/download/hash-base-3.0.5.tgz"
  integrity sha1-UkgOKFOVz3+6F9xMnkes3H8kioo=
  dependencies:
    inherits "^2.0.4"
    safe-buffer "^5.2.1"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/hash-sum/download/hash-sum-1.0.2.tgz"
  integrity sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=

hash-sum@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/hash-sum/download/hash-sum-2.0.0.tgz"
  integrity sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/hash.js/download/hash.js-1.1.7.tgz"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hasown@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

he@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/he/download/he-1.2.0.tgz"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

highlight.js@^10.7.1:
  version "10.7.3"
  resolved "http://r.npm.sankuai.com/highlight.js/download/highlight.js-10.7.3.tgz"
  integrity sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE=

highlight.js@^11.11.1:
  version "11.11.1"
  resolved "http://r.npm.sankuai.com/highlight.js/download/highlight.js-11.11.1.tgz"
  integrity sha1-/KBvoOWu7PbE1DcjkTX6vBUhNYU=

hmac-drbg@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "http://r.npm.sankuai.com/hpack.js/download/hpack.js-2.1.6.tgz"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^2.3.2:
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/html-entities/download/html-entities-2.6.0.tgz"
  integrity sha1-fGTx6js2gYzK49P7SLaXQgjphPg=

html-escaper@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/html-escaper/download/html-escaper-2.0.2.tgz"
  integrity sha1-***************************=

html-minifier-terser@^6.0.2:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/html-minifier-terser/download/html-minifier-terser-6.1.0.tgz"
  integrity sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=
  dependencies:
    camel-case "^4.1.2"
    clean-css "^5.2.2"
    commander "^8.3.0"
    he "^1.2.0"
    param-case "^3.0.4"
    relateurl "^0.2.7"
    terser "^5.10.0"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/html-tags/download/html-tags-2.0.0.tgz"
  integrity sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=

html-webpack-plugin@^5.1.0:
  version "5.6.3"
  resolved "http://r.npm.sankuai.com/html-webpack-plugin/download/html-webpack-plugin-5.6.3.tgz"
  integrity sha1-oxFF8P7kGE1Tp5T5UTFH3x5lNoU=
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    html-minifier-terser "^6.0.2"
    lodash "^4.17.21"
    pretty-error "^4.0.0"
    tapable "^2.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-6.1.0.tgz"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/http-deceiver/download/http-deceiver-1.2.7.tgz"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://r.npm.sankuai.com/http-errors/download/http-errors-1.6.3.tgz"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.10"
  resolved "http://r.npm.sankuai.com/http-parser-js/download/http-parser-js-0.5.10.tgz"
  integrity sha1-syd71tftVYjiDqc79yT8vkRgkHU=

http-proxy-middleware@^2.0.3:
  version "2.0.9"
  resolved "http://r.npm.sankuai.com/http-proxy-middleware/download/http-proxy-middleware-2.0.9.tgz"
  integrity sha1-6eY9aK+qTu49FH85FJq4TAwoFe8=
  dependencies:
    "@types/http-proxy" "^1.17.8"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "http://r.npm.sankuai.com/http-proxy/download/http-proxy-1.18.1.tgz"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-2.1.0.tgz"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/humanize-ms/download/humanize-ms-1.2.1.tgz"
  integrity sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=
  dependencies:
    ms "^2.0.0"

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/icss-utils/download/icss-utils-5.1.0.tgz"
  integrity sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=

idb-keyval@^6.2.0:
  version "6.2.2"
  resolved "http://r.npm.sankuai.com/idb-keyval/download/idb-keyval-6.2.2.tgz"
  integrity sha1-sBcbX3OUSFSjKRpc26jhJ2jEhUo=

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.2.0:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

immediate@~3.0.5:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/immediate/download/immediate-3.0.6.tgz"
  integrity sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3, inherits@~2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

ipaddr.js@^2.0.1:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/ipaddr.js/download/ipaddr.js-2.2.0.tgz"
  integrity sha1-0z+nusKE9N56+UljjJ1oFXxrkug=

is-arguments@^1.0.4:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/is-arguments/download/is-arguments-1.2.0.tgz"
  integrity sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/is-ci/download/is-ci-1.2.1.tgz"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/is-docker/download/is-docker-2.2.1.tgz"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-file-esm@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-file-esm/download/is-file-esm-1.0.0.tgz"
  integrity sha1-mHCGsPWlMYF56dMPTy+NNzIeG18=
  dependencies:
    read-pkg-up "^7.0.1"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-function@^1.0.7:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.1.0.tgz"
  integrity sha1-vz7tqTEgE5T1e126KAD5GiODCco=
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-interactive/download/is-interactive-1.0.0.tgz"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-3.0.0.tgz"
  integrity sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=

is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-regex@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.2.1.tgz"
  integrity sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-1.1.0.tgz"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-typed-array@^1.1.3:
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.15.tgz"
  integrity sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=
  dependencies:
    which-typed-array "^1.1.16"

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-url@^1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/is-url/download/is-url-1.2.4.tgz"
  integrity sha1-BKTfRtKMTP89c9Af8Gq+sxihqlI=

is-wsl@^2.1.1, is-wsl@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/is-wsl/download/is-wsl-2.2.0.tgz"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/isobject/download/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

javascript-stringify@^2.0.1:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/javascript-stringify/download/javascript-stringify-2.1.0.tgz"
  integrity sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk=

jest-worker@^27.0.2, jest-worker@^27.4.5:
  version "27.5.1"
  resolved "http://r.npm.sankuai.com/jest-worker/download/jest-worker-27.5.1.tgz"
  integrity sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^28.0.2:
  version "28.1.3"
  resolved "http://r.npm.sankuai.com/jest-worker/download/jest-worker-28.1.3.tgz"
  integrity sha1-fjxM4/oj0btqzLFp5/OW+Y7Uu5g=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

joi@^17.4.0:
  version "17.13.3"
  resolved "http://r.npm.sankuai.com/joi/download/joi-17.13.3.tgz"
  integrity sha1-D1zBFpyZmzDTRDZtOEsS2SVYvOw=
  dependencies:
    "@hapi/hoek" "^9.3.0"
    "@hapi/topo" "^5.1.0"
    "@sideway/address" "^4.1.5"
    "@sideway/formula" "^3.0.1"
    "@sideway/pinpoint" "^2.0.0"

js-message@1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/js-message/download/js-message-1.0.7.tgz"
  integrity sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc=

js-tiktoken@^1.0.20:
  version "1.0.20"
  resolved "http://r.npm.sankuai.com/js-tiktoken/download/js-tiktoken-1.0.20.tgz"
  integrity sha1-+iczvxR6yvG9z5q4qHjnnFgclfI=
  dependencies:
    base64-js "^1.5.1"

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.1.tgz"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

jsesc@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.0.2.tgz"
  integrity sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/json5/download/json5-1.0.2.tgz"
  integrity sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2, json5@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/jsonfile/download/jsonfile-6.1.0.tgz"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jszip@^3.7.1:
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/jszip/download/jszip-3.10.1.tgz"
  integrity sha1-NK7nDrGOofrsL1iSCKFX0f6wkcI=
  dependencies:
    lie "~3.3.0"
    pako "~1.0.2"
    readable-stream "~2.3.6"
    setimmediate "^1.0.5"

keyv@^4.5.3:
  version "4.5.4"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klona@^2.0.5:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/klona/download/klona-2.0.6.tgz"
  integrity sha1-hb/7+BnAOy9TJwQSQgpFVe+ILiI=

launch-editor-middleware@^2.2.1:
  version "2.10.0"
  resolved "http://r.npm.sankuai.com/launch-editor-middleware/download/launch-editor-middleware-2.10.0.tgz"
  integrity sha1-dNkJUWDwvLRFVqCG6d0W5n1GT/Q=
  dependencies:
    launch-editor "^2.10.0"

launch-editor@^2.10.0, launch-editor@^2.2.1, launch-editor@^2.6.0:
  version "2.10.0"
  resolved "http://r.npm.sankuai.com/launch-editor/download/launch-editor-2.10.0.tgz"
  integrity sha1-XKPt/LlmffHochMQ86QPESfUvEI=
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.8.1"

levn@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lie@~3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/lie/download/lie-3.3.0.tgz"
  integrity sha1-3Pgt7lRfRgdNryAMfBxaCOD0D2o=
  dependencies:
    immediate "~3.0.5"

lilconfig@^2.0.3:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/lilconfig/download/lilconfig-2.1.0.tgz"
  integrity sha1-eOI6yJ67fhv78lsYBD3nVlSOf1I=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

loader-runner@^4.1.0, loader-runner@^4.2.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/loader-runner/download/loader-runner-4.3.0.tgz"
  integrity sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=

loader-utils@^1.0.2, loader-utils@^1.1.0:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/loader-utils/download/loader-utils-1.4.2.tgz"
  integrity sha1-KalX86Y5c4g+toTxD/09FR/sAaM=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^2.0.0, loader-utils@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/loader-utils/download/loader-utils-2.0.4.tgz"
  integrity sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.defaultsdeep@^4.6.1:
  version "4.6.1"
  resolved "http://r.npm.sankuai.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz"
  integrity sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY=

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash.mapvalues@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz"
  integrity sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "http://r.npm.sankuai.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.17.20, lodash@^4.17.21:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-4.1.0.tgz"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/log-update/download/log-update-2.3.0.tgz"
  integrity sha1-iDKP19HOeTiykoN0bwsbwSayRwg=
  dependencies:
    ansi-escapes "^3.0.0"
    cli-cursor "^2.0.0"
    wrap-ansi "^3.0.1"

lop@^0.4.2:
  version "0.4.2"
  resolved "http://r.npm.sankuai.com/lop/download/lop-0.4.2.tgz"
  integrity sha1-ycL5WKObnaHC82yprWaJGp/oRkA=
  dependencies:
    duck "^0.1.12"
    option "~0.2.1"
    underscore "^1.13.1"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/lower-case/download/lower-case-2.0.2.tgz"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^4.0.1, lru-cache@^4.1.2:
  version "4.1.5"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-4.1.5.tgz"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

magic-string@^0.30.17:
  version "0.30.17"
  resolved "http://r.npm.sankuai.com/magic-string/download/magic-string-0.30.17.tgz"
  integrity sha1-RQpElnPSRg5bvPupphkWoXFMdFM=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-3.1.0.tgz"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

mammoth@^1.9.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/mammoth/download/mammoth-1.9.0.tgz"
  integrity sha1-ceNMooBzUnV4i/6V5lOgWNyrTfI=
  dependencies:
    "@xmldom/xmldom" "^0.8.6"
    argparse "~1.0.3"
    base64-js "^1.5.1"
    bluebird "~3.4.0"
    dingbat-to-unicode "^1.0.1"
    jszip "^3.7.1"
    lop "^0.4.2"
    path-is-absolute "^1.0.0"
    underscore "^1.13.1"
    xmlbuilder "^10.0.0"

marked@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/marked/download/marked-2.1.3.tgz"
  integrity sha1-vQF872Qxck/Usn4GV/XOsUv/N1M=

marked@^4.1.0, marked@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/marked/download/marked-4.3.0.tgz"
  integrity sha1-eWNighsBn3NAVFggOLEWSBtFbPM=

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

md5.js@^1.3.4:
  version "1.3.5"
  resolved "http://r.npm.sankuai.com/md5.js/download/md5.js-1.3.5.tgz"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "http://r.npm.sankuai.com/mdn-data/download/mdn-data-2.0.14.tgz"
  integrity sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/media-typer/download/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memfs@^3.4.3:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/memfs/download/memfs-3.6.0.tgz"
  integrity sha1-16IRD4b3ndlQqLbfbVe8mEqhhfY=
  dependencies:
    fs-monkey "^1.0.4"

merge-descriptors@1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/merge-descriptors/download/merge-descriptors-1.0.3.tgz"
  integrity sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/merge-source-map/download/merge-source-map-1.1.0.tgz"
  integrity sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=
  dependencies:
    source-map "^0.6.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/methods/download/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^4.0.2, micromatch@^4.0.5, micromatch@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/miller-rabin/download/miller-rabin-4.0.1.tgz"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

"mime-db@>= 1.43.0 < 2":
  version "1.54.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.54.0.tgz"
  integrity sha1-zds+5PnGRTDf9kAjZmHULLajFPU=

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/mime/download/mime-1.6.0.tgz"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mini-css-extract-plugin@^2.5.3:
  version "2.9.2"
  resolved "http://r.npm.sankuai.com/mini-css-extract-plugin/download/mini-css-extract-plugin-2.9.2.tgz"
  integrity sha1-lmAxtGiRelRG9MJKgIVLKUdQPFs=
  dependencies:
    schema-utils "^4.0.0"
    tapable "^2.2.1"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

minipass@^3.1.1:
  version "3.3.6"
  resolved "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz"
  integrity sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=
  dependencies:
    yallist "^4.0.0"

module-alias@^2.2.2:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/module-alias/download/module-alias-2.2.3.tgz"
  integrity sha1-7C6Fxolzvaarcc58k7dj7JYFMiE=

mrmime@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/mrmime/download/mrmime-2.0.1.tgz"
  integrity sha1-vD6H95h4U6VMmFDusfEHjNRK3dw=

ms@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.3, ms@^2.0.0, ms@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multicast-dns@^7.2.5:
  version "7.2.5"
  resolved "http://r.npm.sankuai.com/multicast-dns/download/multicast-dns-7.2.5.tgz"
  integrity sha1-d+tGBX9NetvRbZKQ+nKZ9vpkzO0=
  dependencies:
    dns-packet "^5.2.2"
    thunky "^1.0.2"

mz@^2.4.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/mz/download/mz-2.7.0.tgz"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.8:
  version "3.3.11"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.3:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.3.tgz"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

negotiator@~0.6.4:
  version "0.6.4"
  resolved "http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.4.tgz"
  integrity sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=

neo-async@^2.6.2:
  version "2.6.2"
  resolved "http://r.npm.sankuai.com/neo-async/download/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/nice-try/download/nice-try-1.0.5.tgz"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/no-case/download/no-case-3.0.4.tgz"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-domexception@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/node-domexception/download/node-domexception-1.0.0.tgz"
  integrity sha1-aIjbRqH3HAt2s/dVUBa2P+ZHZuU=

node-fetch@^2.6.7, node-fetch@^2.6.9:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/node-fetch/download/node-fetch-2.7.0.tgz"
  integrity sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=
  dependencies:
    whatwg-url "^5.0.0"

node-forge@^1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/node-forge/download/node-forge-1.3.1.tgz"
  integrity sha1-vo2iryQ7JBfV9kancGY6krfp3tM=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-1.0.0.tgz"
  integrity sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/normalize-range/download/normalize-range-0.1.2.tgz"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/normalize-url/download/normalize-url-6.1.0.tgz"
  integrity sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=

normalize-wheel@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/normalize-wheel/download/normalize-wheel-1.0.1.tgz"
  integrity sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU=

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

object-assign@^4.0.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.13.3:
  version "1.13.4"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.4.tgz"
  integrity sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.0:
  version "4.1.7"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.7.tgz"
  integrity sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/obuf/download/obuf-1.1.2.tgz"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@2.4.1:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/on-finished/download/on-finished-2.4.1.tgz"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/on-headers/download/on-headers-1.0.2.tgz"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^8.0.2, open@^8.0.9:
  version "8.4.2"
  resolved "http://r.npm.sankuai.com/open/download/open-8.4.2.tgz"
  integrity sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

openai@^4.96.0:
  version "4.98.0"
  resolved "http://r.npm.sankuai.com/openai/download/openai-4.98.0.tgz"
  integrity sha1-gdgijgbl2RlbrDsXCvQqVFQ5GZk=
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"

opencollective-postinstall@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/opencollective-postinstall/download/opencollective-postinstall-2.0.3.tgz"
  integrity sha1-eg//l49tv6TQBiOPusmO1BmMMlk=

opener@^1.5.2:
  version "1.5.2"
  resolved "http://r.npm.sankuai.com/opener/download/opener-1.5.2.tgz"
  integrity sha1-XTfh81B3udysQwE3InGv3rKhNZg=

option@~0.2.1:
  version "0.2.4"
  resolved "http://r.npm.sankuai.com/option/download/option-0.2.4.tgz"
  integrity sha1-/Udc35jcq7PLOXo7pShP60Xtv+Q=

optionator@^0.9.1:
  version "0.9.4"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^5.3.0:
  version "5.4.1"
  resolved "http://r.npm.sankuai.com/ora/download/ora-5.4.1.tgz"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/os-browserify/download/os-browserify-0.3.0.tgz"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/p-finally/download/p-finally-1.0.0.tgz"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-retry@^4.5.0:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/p-retry/download/p-retry-4.6.2.tgz"
  integrity sha1-m6rnGEBX7dThcjHO4EJkEG4JKhY=
  dependencies:
    "@types/retry" "0.12.0"
    retry "^0.13.1"

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.2:
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/pako/download/pako-1.0.11.tgz"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

param-case@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/param-case/download/param-case-3.0.4.tgz"
  integrity sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0, parse-asn1@^5.1.7:
  version "5.1.7"
  resolved "http://r.npm.sankuai.com/parse-asn1/download/parse-asn1-5.1.7.tgz"
  integrity sha1-c82qqCISX5ZHFlYl60X4oFHS3wY=
  dependencies:
    asn1.js "^4.10.1"
    browserify-aes "^1.2.0"
    evp_bytestokey "^1.0.3"
    hash-base "~3.0"
    pbkdf2 "^3.1.2"
    safe-buffer "^5.2.1"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^6.0.0:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  integrity sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=
  dependencies:
    parse5 "^6.0.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-5.1.1.tgz"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parse5@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-6.0.1.tgz"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/pascal-case/download/pascal-case-3.1.2.tgz"
  integrity sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-browserify/download/path-browserify-1.0.1.tgz"
  integrity sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.12:
  version "0.1.12"
  resolved "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-0.1.12.tgz"
  integrity sha1-1eGhLkeKl21DLvPFjVNLmSMWS7c=

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pbkdf2@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/pbkdf2/download/pbkdf2-3.1.2.tgz"
  integrity sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

picocolors@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-0.2.1.tgz"
  integrity sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=

picocolors@^1.0.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-4.2.0.tgz"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

portfinder@^1.0.26:
  version "1.0.37"
  resolved "http://r.npm.sankuai.com/portfinder/download/portfinder-1.0.37.tgz"
  integrity sha1-krdU74mhGAHI7+Sw5c2EWwBkwhI=
  dependencies:
    async "^3.2.6"
    debug "^4.3.6"

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.1.0.tgz"
  integrity sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=

postcss-calc@^8.2.3:
  version "8.2.4"
  resolved "http://r.npm.sankuai.com/postcss-calc/download/postcss-calc-8.2.4.tgz"
  integrity sha1-d7nCm/y+igf/ZpPchwUIKIiXOaU=
  dependencies:
    postcss-selector-parser "^6.0.9"
    postcss-value-parser "^4.2.0"

postcss-colormin@^5.3.1:
  version "5.3.1"
  resolved "http://r.npm.sankuai.com/postcss-colormin/download/postcss-colormin-5.3.1.tgz"
  integrity sha1-hsJ8Ju1roA2Wx54I8/+0GNHRmI8=
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    colord "^2.9.1"
    postcss-value-parser "^4.2.0"

postcss-convert-values@^5.1.3:
  version "5.1.3"
  resolved "http://r.npm.sankuai.com/postcss-convert-values/download/postcss-convert-values-5.1.3.tgz"
  integrity sha1-BJmLubprZaoxA11mmmrzQsX505M=
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-discard-comments@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/postcss-discard-comments/download/postcss-discard-comments-5.1.2.tgz"
  integrity sha1-jfXoHSklryeAB1hAwVJvBmDlNpY=

postcss-discard-duplicates@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-discard-duplicates/download/postcss-discard-duplicates-5.1.0.tgz"
  integrity sha1-nrT+hFZwak7r1tO3t3fQe60D6Eg=

postcss-discard-empty@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/postcss-discard-empty/download/postcss-discard-empty-5.1.1.tgz"
  integrity sha1-5XdiND/39QP+U/ylU9GNfww2nGw=

postcss-discard-overridden@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-discard-overridden/download/postcss-discard-overridden-5.1.0.tgz"
  integrity sha1-foxbUzJXR+nZATG7iGNSgvtKJ24=

postcss-loader@^6.1.1:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/postcss-loader/download/postcss-loader-6.2.1.tgz"
  integrity sha1-CJX3NGsXAhA9MP3Gbk1JSpPACO8=
  dependencies:
    cosmiconfig "^7.0.0"
    klona "^2.0.5"
    semver "^7.3.5"

postcss-merge-longhand@^5.1.7:
  version "5.1.7"
  resolved "http://r.npm.sankuai.com/postcss-merge-longhand/download/postcss-merge-longhand-5.1.7.tgz"
  integrity sha1-JKG99ALZ7w5w9Wjzm9wDRNVo+xY=
  dependencies:
    postcss-value-parser "^4.2.0"
    stylehacks "^5.1.1"

postcss-merge-rules@^5.1.4:
  version "5.1.4"
  resolved "http://r.npm.sankuai.com/postcss-merge-rules/download/postcss-merge-rules-5.1.4.tgz"
  integrity sha1-Lyb6XKy3WxQC4hN4n2dmrl5AMTw=
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    cssnano-utils "^3.1.0"
    postcss-selector-parser "^6.0.5"

postcss-minify-font-values@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-minify-font-values/download/postcss-minify-font-values-5.1.0.tgz"
  integrity sha1-8d8AFKcmCD0mDTvYXXOF+4nR8Bs=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-minify-gradients@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/postcss-minify-gradients/download/postcss-minify-gradients-5.1.1.tgz"
  integrity sha1-8f4bT0mBNKUGgkDC8l1G/NI2uiw=
  dependencies:
    colord "^2.9.1"
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-minify-params@^5.1.4:
  version "5.1.4"
  resolved "http://r.npm.sankuai.com/postcss-minify-params/download/postcss-minify-params-5.1.4.tgz"
  integrity sha1-wGpseHEosyCLOMk2TPxAyKpdc1I=
  dependencies:
    browserslist "^4.21.4"
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-minify-selectors@^5.2.1:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/postcss-minify-selectors/download/postcss-minify-selectors-5.2.1.tgz"
  integrity sha1-1OfmtGFHuBF+qTJakVqAHV/mVsY=
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-modules-extract-imports@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-3.1.0.tgz"
  integrity sha1-tEl8uFqcDEtaq+t1m7JejYnxUAI=

postcss-modules-local-by-default@^4.0.5:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-4.2.0.tgz"
  integrity sha1-0VD0ODeDHa4l5AhVluhPb11uw2g=
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^7.0.0"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.2.0:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/postcss-modules-scope/download/postcss-modules-scope-3.2.1.tgz"
  integrity sha1-G7zN3LOY8delEeCi0dBHcYr0B4w=
  dependencies:
    postcss-selector-parser "^7.0.0"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/postcss-modules-values/download/postcss-modules-values-4.0.0.tgz"
  integrity sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=
  dependencies:
    icss-utils "^5.0.0"

postcss-normalize-charset@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-normalize-charset/download/postcss-normalize-charset-5.1.0.tgz"
  integrity sha1-kwLeCykJS1LCWemyz43Ah5h58O0=

postcss-normalize-display-values@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-normalize-display-values/download/postcss-normalize-display-values-5.1.0.tgz"
  integrity sha1-cqu65YCBlg6e3XIA/PIauDJcPag=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-positions@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/postcss-normalize-positions/download/postcss-normalize-positions-5.1.1.tgz"
  integrity sha1-75cnnYlAh7WTJbRcR/HoY9rvu5I=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-repeat-style@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-5.1.1.tgz"
  integrity sha1-6euWgFIE9HZt9m/QntLhNUVCD7I=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-string@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-normalize-string/download/postcss-normalize-string-5.1.0.tgz"
  integrity sha1-QRlhFp4HMIyCwfjFXz6KM3dX4ig=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-timing-functions@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-5.1.0.tgz"
  integrity sha1-1WFEEPjwsjiOnyQKpgEbpvUtr7s=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-unicode@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/postcss-normalize-unicode/download/postcss-normalize-unicode-5.1.1.tgz"
  integrity sha1-9nKX/KP+p/F+DSyqQHaa/Eh6oDA=
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-normalize-url@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-normalize-url/download/postcss-normalize-url-5.1.0.tgz"
  integrity sha1-7Z2IyoLiGr75n3Q0V9NymgQq3Nw=
  dependencies:
    normalize-url "^6.0.1"
    postcss-value-parser "^4.2.0"

postcss-normalize-whitespace@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-5.1.1.tgz"
  integrity sha1-CKGg0f+henzG7+HmydqWnMRJPPo=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-ordered-values@^5.1.3:
  version "5.1.3"
  resolved "http://r.npm.sankuai.com/postcss-ordered-values/download/postcss-ordered-values-5.1.3.tgz"
  integrity sha1-tv0r0Q+TeyPYa8gpxp53Ms526jg=
  dependencies:
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-reduce-initial@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/postcss-reduce-initial/download/postcss-reduce-initial-5.1.2.tgz"
  integrity sha1-eYzXez4DPq5xBcGMnTcdmJ4TgtY=
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"

postcss-reduce-transforms@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-reduce-transforms/download/postcss-reduce-transforms-5.1.0.tgz"
  integrity sha1-Mztw53WLgC890N3+mLscz++Wtuk=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4, postcss-selector-parser@^6.0.5, postcss-selector-parser@^6.0.9:
  version "6.1.2"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz"
  integrity sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-selector-parser@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-7.1.0.tgz"
  integrity sha1-TWr5frpl1zvE2EvLND6GXX3RYmI=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/postcss-svgo/download/postcss-svgo-5.1.0.tgz"
  integrity sha1-CjF0AM7XifIzoogm53Uj8VhX2A0=
  dependencies:
    postcss-value-parser "^4.2.0"
    svgo "^2.7.0"

postcss-unique-selectors@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/postcss-unique-selectors/download/postcss-unique-selectors-5.1.1.tgz"
  integrity sha1-qfJz0erNCemqYIj0sFB7GLG1QbY=
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^7.0.36:
  version "7.0.39"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-7.0.39.tgz"
  integrity sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^8.2.6, postcss@^8.3.5, postcss@^8.4.14, postcss@^8.4.33, postcss@^8.5.3:
  version "8.5.3"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-8.5.3.tgz"
  integrity sha1-FGO28cf7Fv4lhzbLopot41I36vs=
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

"prettier@^1.18.2 || ^2.0.0":
  version "2.8.8"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-2.8.8.tgz"
  integrity sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=

pretty-error@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/pretty-error/download/pretty-error-4.0.0.tgz"
  integrity sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=
  dependencies:
    lodash "^4.17.20"
    renderkid "^3.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "http://r.npm.sankuai.com/process/download/process-0.11.10.tgz"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress-webpack-plugin@^1.0.12:
  version "1.0.16"
  resolved "http://r.npm.sankuai.com/progress-webpack-plugin/download/progress-webpack-plugin-1.0.16.tgz"
  integrity sha1-J49cGv0hr3g6rXLF7JUkFSAjD+U=
  dependencies:
    chalk "^2.1.0"
    figures "^2.0.0"
    log-update "^2.3.0"

progress@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/progress/download/progress-2.0.3.tgz"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "http://r.npm.sankuai.com/proxy-addr/download/proxy-addr-2.0.7.tgz"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/pseudomap/download/pseudomap-1.0.2.tgz"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

public-encrypt@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/public-encrypt/download/public-encrypt-4.0.3.tgz"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^3.0.0:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/pump/download/pump-3.0.2.tgz"
  integrity sha1-g28+3WvC7lmSVskk/+DYhXPdy/g=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

qs@6.13.0:
  version "6.13.0"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.13.0.tgz"
  integrity sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=
  dependencies:
    side-channel "^1.0.6"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/randombytes/download/randombytes-2.1.0.tgz"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/randomfill/download/randomfill-1.0.4.tgz"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.5.2:
  version "2.5.2"
  resolved "http://r.npm.sankuai.com/raw-body/download/raw-body-2.5.2.tgz"
  integrity sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.1.1, read-pkg@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/read-pkg/download/read-pkg-5.2.0.tgz"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^2.0.1, readable-stream@^2.3.8, readable-stream@~2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.4.0, readable-stream@^3.5.0, readable-stream@^3.6.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

regenerate-unicode-properties@^10.2.0:
  version "10.2.0"
  resolved "http://r.npm.sankuai.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.2.0.tgz"
  integrity sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/regenerate/download/regenerate-1.4.2.tgz"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.3:
  version "0.13.11"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz"
  integrity sha1-9tyj587sIFkNB62nhWNqkM3KF/k=

regexpp@^3.1.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/regexpp/download/regexpp-3.2.0.tgz"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

regexpu-core@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/regexpu-core/download/regexpu-core-6.2.0.tgz"
  integrity sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.0"
    regjsgen "^0.8.0"
    regjsparser "^0.12.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsgen@^0.8.0:
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/regjsgen/download/regjsgen-0.8.0.tgz"
  integrity sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=

regjsparser@^0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/regjsparser/download/regjsparser-0.12.0.tgz"
  integrity sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=
  dependencies:
    jsesc "~3.0.2"

relateurl@^0.2.7:
  version "0.2.7"
  resolved "http://r.npm.sankuai.com/relateurl/download/relateurl-0.2.7.tgz"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

renderkid@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/renderkid/download/renderkid-3.0.0.tgz"
  integrity sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^6.0.1"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/require-from-string/download/require-from-string-2.0.2.tgz"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resize-observer-polyfill@^1.5.0:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve@^1.10.0, resolve@^1.14.2:
  version "1.22.10"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

retry@^0.13.1:
  version "0.13.1"
  resolved "http://r.npm.sankuai.com/retry/download/retry-0.13.1.tgz"
  integrity sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=

reusify@^1.0.4:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.1.0.tgz"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/ripemd160/download/ripemd160-2.0.2.tgz"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@5.2.1, safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@^5.2.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.1.0.tgz"
  integrity sha1-f4fftnoxUHguqvGFg/9dFxGsEME=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

schema-utils@^2.6.5:
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/schema-utils/download/schema-utils-2.7.1.tgz"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.0.0, schema-utils@^3.1.1:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/schema-utils/download/schema-utils-3.3.0.tgz"
  integrity sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.0.0, schema-utils@^4.3.0, schema-utils@^4.3.2:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/schema-utils/download/schema-utils-4.3.2.tgz"
  integrity sha1-DBCHi/SnP9Kx39FLlGKyZ4jIBq4=
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.9.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.1.0"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/select-hose/download/select-hose-2.0.0.tgz"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^2.1.1:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/selfsigned/download/selfsigned-2.4.1.tgz"
  integrity sha1-Vg2QVlRCo+01tnQDTOxOldzrSuA=
  dependencies:
    "@types/node-forge" "^1.3.0"
    node-forge "^1"

"semver@2 || 3 || 4 || 5", semver@^5.5.0:
  version "5.7.2"
  resolved "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz"
  integrity sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=

semver@^6.0.0, semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.2.1, semver@^7.3.4, semver@^7.3.5, semver@^7.5.4:
  version "7.7.2"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

send@0.19.0:
  version "0.19.0"
  resolved "http://r.npm.sankuai.com/send/download/send-0.19.0.tgz"
  integrity sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^6.0.0, serialize-javascript@^6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/serialize-javascript/download/serialize-javascript-6.0.2.tgz"
  integrity sha1-3voeBVyDv21Z6oBdjahiJU62psI=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/serve-index/download/serve-index-1.9.1.tgz"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.16.2:
  version "1.16.2"
  resolved "http://r.npm.sankuai.com/serve-static/download/serve-static-1.16.2.tgz"
  integrity sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=
  dependencies:
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.19.0"

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/setimmediate/download/setimmediate-1.0.5.tgz"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.1.0.tgz"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "http://r.npm.sankuai.com/sha.js/download/sha.js-2.4.11.tgz"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/shallow-clone/download/shallow-clone-3.0.1.tgz"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.8.1:
  version "1.8.2"
  resolved "http://r.npm.sankuai.com/shell-quote/download/shell-quote-1.8.2.tgz"
  integrity sha1-0tg+BXlZ1T7CYTEenpuPUdyyk0o=

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.6:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

sirv@^2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/sirv/download/sirv-2.0.4.tgz"
  integrity sha1-XdmnJcV4405EnzMnA+sqdORqKbA=
  dependencies:
    "@polka/url" "^1.0.0-next.24"
    mrmime "^2.0.0"
    totalist "^3.0.0"

slash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-4.0.0.tgz"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

sockjs@^0.3.24:
  version "0.3.24"
  resolved "http://r.npm.sankuai.com/sockjs/download/sockjs-0.3.24.tgz"
  integrity sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.21.tgz"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/spdx-correct/download/spdx-correct-3.2.0.tgz"
  integrity sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/spdx-exceptions/download/spdx-exceptions-2.5.0.tgz"
  integrity sha1-XWB9J/yAb2bXtkp2ZlD6iQ8E7WY=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.21"
  resolved "http://r.npm.sankuai.com/spdx-license-ids/download/spdx-license-ids-3.0.21.tgz"
  integrity sha1-bW6YDJ3ytvyQU0OjstcCpiOVNsM=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/spdy-transport/download/spdy-transport-3.0.0.tgz"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/spdy/download/spdy-4.0.2.tgz"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

ssri@^8.0.1:
  version "8.0.1"
  resolved "http://r.npm.sankuai.com/ssri/download/ssri-8.0.1.tgz"
  integrity sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=
  dependencies:
    minipass "^3.1.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/stable/download/stable-0.1.8.tgz"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stackframe@^1.3.4:
  version "1.3.4"
  resolved "http://r.npm.sankuai.com/stackframe/download/stackframe-1.3.4.tgz"
  integrity sha1-uIGgBMjBSaXo7+831RsW5BKUMxA=

statuses@2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

"statuses@>= 1.4.0 < 2":
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/statuses/download/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stream-browserify@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/stream-browserify/download/stream-browserify-3.0.0.tgz"
  integrity sha1-IrCihQzfZQPnMIXaH8e30MISLy8=
  dependencies:
    inherits "~2.0.4"
    readable-stream "^3.5.0"

string-width@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/strip-eof/download/strip-eof-1.0.0.tgz"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/strip-indent/download/strip-indent-2.0.0.tgz"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

stylehacks@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/stylehacks/download/stylehacks-5.1.1.tgz"
  integrity sha1-eTSjTrWdcVIUn6adbp5W8vw0vMk=
  dependencies:
    browserslist "^4.21.4"
    postcss-selector-parser "^6.0.4"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-8.1.1.tgz"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/svg-tags/download/svg-tags-1.0.0.tgz"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

svgo@^2.7.0:
  version "2.8.0"
  resolved "http://r.npm.sankuai.com/svgo/download/svgo-2.8.0.tgz"
  integrity sha1-T/gMzmcQ3CeV8MfHQQHmdkz8zSQ=
  dependencies:
    "@trysound/sax" "0.2.0"
    commander "^7.2.0"
    css-select "^4.1.3"
    css-tree "^1.1.3"
    csso "^4.2.0"
    picocolors "^1.0.0"
    stable "^0.1.8"

table@^6.0.9:
  version "6.9.0"
  resolved "http://r.npm.sankuai.com/table/download/table-6.9.0.tgz"
  integrity sha1-UAQK+mJkFBx1ZrO4HU2CxHqGaPU=
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

tapable@^2.0.0, tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/tapable/download/tapable-2.2.1.tgz"
  integrity sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=

terser-webpack-plugin@^5.1.1, terser-webpack-plugin@^5.3.11:
  version "5.3.14"
  resolved "http://r.npm.sankuai.com/terser-webpack-plugin/download/terser-webpack-plugin-5.3.14.tgz"
  integrity sha1-kDHUjlerJ1Z/AqzoXH1pDbZsPgY=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    jest-worker "^27.4.5"
    schema-utils "^4.3.0"
    serialize-javascript "^6.0.2"
    terser "^5.31.1"

terser@^5.10.0, terser@^5.31.1:
  version "5.39.2"
  resolved "http://r.npm.sankuai.com/terser/download/terser-5.39.2.tgz"
  integrity sha1-WhYmAwckpnLi5bXJzZBwMIwg6Pk=
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.14.0"
    commander "^2.20.0"
    source-map-support "~0.5.20"

tesseract.js-core@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/tesseract.js-core/download/tesseract.js-core-6.0.0.tgz"
  integrity sha1-byXalPcPjo8Cr/R6Q75h1J5vZ8M=

tesseract.js@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/tesseract.js/download/tesseract.js-6.0.1.tgz"
  integrity sha1-Wy/zmq6S1ZzveViaQ6Dzq5Y4Acw=
  dependencies:
    bmp-js "^0.1.0"
    idb-keyval "^6.2.0"
    is-url "^1.2.4"
    node-fetch "^2.6.9"
    opencollective-postinstall "^2.0.3"
    regenerator-runtime "^0.13.3"
    tesseract.js-core "^6.0.0"
    wasm-feature-detect "^1.2.11"
    zlibjs "^0.3.1"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/thenify-all/download/thenify-all-1.6.0.tgz"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/thenify/download/thenify-3.3.1.tgz"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

thread-loader@^3.0.0:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/thread-loader/download/thread-loader-3.0.4.tgz"
  integrity sha1-w5LkwCQfvIBDDraA5IhoGbUEoxs=
  dependencies:
    json-parse-better-errors "^1.0.2"
    loader-runner "^4.1.0"
    loader-utils "^2.0.0"
    neo-async "^2.6.2"
    schema-utils "^3.0.0"

throttle-debounce@^1.0.1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-1.1.0.tgz"
  integrity sha1-UYU9o3vmihVctugns1FKPEIuic0=

thunky@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/thunky/download/thunky-1.1.0.tgz"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.1.tgz"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

totalist@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/totalist/download/totalist-3.0.1.tgz"
  integrity sha1-ujo9YAyRWxqXhyNI95wSdHX2rPg=

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-0.0.3.tgz"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

tslib@^2.0.3:
  version "2.8.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.6.0.tgz"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.8.1.tgz"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@~1.6.18:
  version "1.6.18"
  resolved "http://r.npm.sankuai.com/type-is/download/type-is-1.6.18.tgz"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typo-js@*:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/typo-js/download/typo-js-1.2.5.tgz"
  integrity sha1-CqZeC+m2kDZGOjgn3oGFtBROMIY=

underscore@^1.13.1:
  version "1.13.7"
  resolved "http://r.npm.sankuai.com/underscore/download/underscore-1.13.7.tgz"
  integrity sha1-lw4zljr5p92iKPF+voOZ5fvmOhA=

undici-types@~5.26.4:
  version "5.26.5"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-5.26.5.tgz"
  integrity sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=

undici-types@~6.21.0:
  version "6.21.0"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-6.21.0.tgz"
  integrity sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.1.tgz"
  integrity sha1-yzFz/kfKdD4ighbko93EyE1ijMI=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.2.0.tgz"
  integrity sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz"
  integrity sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=

universalify@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-2.0.1.tgz"
  integrity sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util@^0.12.5:
  version "0.12.5"
  resolved "http://r.npm.sankuai.com/util/download/util-0.12.5.tgz"
  integrity sha1-XxemBZtz22GodWaHgaHCsTa9b7w=
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

utila@~0.4:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/utila/download/utila-0.4.0.tgz"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/utils-merge/download/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^8.3.2:
  version "8.3.2"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

uzip@0.20201231.0:
  version "0.20201231.0"
  resolved "http://r.npm.sankuai.com/uzip/download/uzip-0.20201231.0.tgz"
  integrity sha1-nmSwZbmo6/Jut1g/6Od+HZoV7RQ=

v8-compile-cache@^2.0.3:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/v8-compile-cache/download/v8-compile-cache-2.4.0.tgz"
  integrity sha1-za2ovsYeFYZfBdCXxfT9MOlNwSg=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/vary/download/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vue-easymde@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/vue-easymde/download/vue-easymde-2.0.0.tgz"
  integrity sha1-y0+ZShOl2zq1CLjPEyrmo/YIgtc=
  dependencies:
    easymde "^2.15.0"
    marked "^2.1.3"

vue-eslint-parser@^8.0.1:
  version "8.3.0"
  resolved "http://r.npm.sankuai.com/vue-eslint-parser/download/vue-eslint-parser-8.3.0.tgz"
  integrity sha1-XTESmhs92JwAacoKHIj5cMNgvQ0=
  dependencies:
    debug "^4.3.2"
    eslint-scope "^7.0.0"
    eslint-visitor-keys "^3.1.0"
    espree "^9.0.0"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.5"

vue-hot-reload-api@^2.3.0:
  version "2.3.4"
  resolved "http://r.npm.sankuai.com/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz"
  integrity sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=

vue-loader@^17.0.0:
  version "17.4.2"
  resolved "http://r.npm.sankuai.com/vue-loader/download/vue-loader-17.4.2.tgz"
  integrity sha1-+H8Nit/LvoYj3p66GXnUG6Ijxto=
  dependencies:
    chalk "^4.1.0"
    hash-sum "^2.0.0"
    watchpack "^2.4.0"

vue-router@^3.5.1:
  version "3.6.5"
  resolved "http://r.npm.sankuai.com/vue-router/download/vue-router-3.6.5.tgz"
  integrity sha1-lYR9Urmn4/E2HLYFyOZEHyAq+tg=

vue-style-loader@^4.1.0, vue-style-loader@^4.1.3:
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/vue-style-loader/download/vue-style-loader-4.1.3.tgz"
  integrity sha1-bVWGOlH6dXqyTonZNxRlByqnvDU=
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-compiler@^2.6.14:
  version "2.7.16"
  resolved "http://r.npm.sankuai.com/vue-template-compiler/download/vue-template-compiler-2.7.16.tgz"
  integrity sha1-yBstR3UyZMd6wDuZZqRmN0grsDs=
  dependencies:
    de-indent "^1.0.2"
    he "^1.2.0"

vue-template-es2015-compiler@^1.9.0:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz"
  integrity sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=

vue@^2.6.14:
  version "2.7.16"
  resolved "http://r.npm.sankuai.com/vue/download/vue-2.7.16.tgz"
  integrity sha1-mMYN6d75nA49qNrlmzBOrUO5Z8k=
  dependencies:
    "@vue/compiler-sfc" "2.7.16"
    csstype "^3.1.0"

vuex@^3.6.2:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/vuex/download/vuex-3.6.2.tgz"
  integrity sha1-I2vAhqhww655lG8QfxbeWdWJXnE=

wasm-feature-detect@^1.2.11:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/wasm-feature-detect/download/wasm-feature-detect-1.8.0.tgz"
  integrity sha1-Tp9VsKZNgB83L7sDJO0RrTq9DHg=

watchpack@^2.4.0, watchpack@^2.4.1:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/watchpack/download/watchpack-2.4.2.tgz"
  integrity sha1-L+6u1nQS58MxhOWnnKc4+9OFZNo=
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "http://r.npm.sankuai.com/wbuf/download/wbuf-1.7.3.tgz"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

web-streams-polyfill@4.0.0-beta.3:
  version "4.0.0-beta.3"
  resolved "http://r.npm.sankuai.com/web-streams-polyfill/download/web-streams-polyfill-4.0.0-beta.3.tgz"
  integrity sha1-KJhIa3T1FWCV5HPv6Ync8YUEejg=

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webpack-bundle-analyzer@^4.4.0:
  version "4.10.2"
  resolved "http://r.npm.sankuai.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-4.10.2.tgz"
  integrity sha1-YzryhiwhNzC+Pb30BFbbFxtg1b0=
  dependencies:
    "@discoveryjs/json-ext" "0.5.7"
    acorn "^8.0.4"
    acorn-walk "^8.0.0"
    commander "^7.2.0"
    debounce "^1.2.1"
    escape-string-regexp "^4.0.0"
    gzip-size "^6.0.0"
    html-escaper "^2.0.2"
    opener "^1.5.2"
    picocolors "^1.0.0"
    sirv "^2.0.3"
    ws "^7.3.1"

webpack-chain@^6.5.1:
  version "6.5.1"
  resolved "http://r.npm.sankuai.com/webpack-chain/download/webpack-chain-6.5.1.tgz"
  integrity sha1-TycoTLu2N+PI+970Pu9YjU2GEgY=
  dependencies:
    deepmerge "^1.5.2"
    javascript-stringify "^2.0.1"

webpack-dev-middleware@^5.3.4:
  version "5.3.4"
  resolved "http://r.npm.sankuai.com/webpack-dev-middleware/download/webpack-dev-middleware-5.3.4.tgz"
  integrity sha1-63s5KBy84Q4QTrK4vytj/OSaNRc=
  dependencies:
    colorette "^2.0.10"
    memfs "^3.4.3"
    mime-types "^2.1.31"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@^4.7.3:
  version "4.15.2"
  resolved "http://r.npm.sankuai.com/webpack-dev-server/download/webpack-dev-server-4.15.2.tgz"
  integrity sha1-ngxwpCoBJWCGCtsYaYbaEkgzMXM=
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/express" "^4.17.13"
    "@types/serve-index" "^1.9.1"
    "@types/serve-static" "^1.13.10"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.5.5"
    ansi-html-community "^0.0.8"
    bonjour-service "^1.0.11"
    chokidar "^3.5.3"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^2.0.0"
    default-gateway "^6.0.3"
    express "^4.17.3"
    graceful-fs "^4.2.6"
    html-entities "^2.3.2"
    http-proxy-middleware "^2.0.3"
    ipaddr.js "^2.0.1"
    launch-editor "^2.6.0"
    open "^8.0.9"
    p-retry "^4.5.0"
    rimraf "^3.0.2"
    schema-utils "^4.0.0"
    selfsigned "^2.1.1"
    serve-index "^1.9.1"
    sockjs "^0.3.24"
    spdy "^4.0.2"
    webpack-dev-middleware "^5.3.4"
    ws "^8.13.0"

webpack-merge@^5.7.3:
  version "5.10.0"
  resolved "http://r.npm.sankuai.com/webpack-merge/download/webpack-merge-5.10.0.tgz"
  integrity sha1-o61ddzJB6caCgDq/Yo1M1iuKQXc=
  dependencies:
    clone-deep "^4.0.1"
    flat "^5.0.2"
    wildcard "^2.0.0"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "http://r.npm.sankuai.com/webpack-sources/download/webpack-sources-3.2.3.tgz"
  integrity sha1-LU2quEUf1LJAzCcFX/agwszqDN4=

webpack-virtual-modules@^0.4.2:
  version "0.4.6"
  resolved "http://r.npm.sankuai.com/webpack-virtual-modules/download/webpack-virtual-modules-0.4.6.tgz"
  integrity sha1-PkAIIwcx8dsHjZy29ouvhXEYK0U=

webpack@^5.54.0:
  version "5.99.8"
  resolved "http://r.npm.sankuai.com/webpack/download/webpack-5.99.8.tgz"
  integrity sha1-3TGgILfAktMMTG2aTtuVgJ5/WUY=
  dependencies:
    "@types/eslint-scope" "^3.7.7"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    "@webassemblyjs/ast" "^1.14.1"
    "@webassemblyjs/wasm-edit" "^1.14.1"
    "@webassemblyjs/wasm-parser" "^1.14.1"
    acorn "^8.14.0"
    browserslist "^4.24.0"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.17.1"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.11"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^4.3.2"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.11"
    watchpack "^2.4.1"
    webpack-sources "^3.2.3"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "http://r.npm.sankuai.com/websocket-driver/download/websocket-driver-0.7.4.tgz"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

whatwg-fetch@^3.6.2:
  version "3.6.20"
  resolved "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-3.6.20.tgz"
  integrity sha1-WAzm15H6zskdN8cokJlaC0jTHHA=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-5.0.0.tgz"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-typed-array@^1.1.16, which-typed-array@^1.1.2:
  version "1.1.19"
  resolved "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.19.tgz"
  integrity sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^1.2.9:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/which/download/which-1.3.1.tgz"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wildcard@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/wildcard/download/wildcard-2.0.1.tgz"
  integrity sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wrap-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz"
  integrity sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

ws@^7.3.1:
  version "7.5.10"
  resolved "http://r.npm.sankuai.com/ws/download/ws-7.5.10.tgz"
  integrity sha1-WLXCDcKBYz9sGRE/ObNJvYvVWNk=

ws@^8.13.0:
  version "8.18.2"
  resolved "http://r.npm.sankuai.com/ws/download/ws-8.18.2.tgz"
  integrity sha1-QnOLK+V87YX0YVQyCqu1GrADcFo=

xmlbuilder@^10.0.0:
  version "10.1.1"
  resolved "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-10.1.1.tgz"
  integrity sha1-jK5miMybONhQt8jTwKQWHcr0dbA=

y18n@^5.0.5:
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-2.1.2.tgz"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0, yaml@^1.10.2:
  version "1.10.2"
  resolved "http://r.npm.sankuai.com/yaml/download/yaml-1.10.2.tgz"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.9.tgz"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs@^16.0.0:
  version "16.2.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-16.2.0.tgz"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yorkie@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/yorkie/download/yorkie-2.0.0.tgz"
  integrity sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"

zlibjs@^0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/zlibjs/download/zlibjs-0.3.1.tgz"
  integrity sha1-UBl+2yihxCymWcyLTmqd3W1ERVQ=
